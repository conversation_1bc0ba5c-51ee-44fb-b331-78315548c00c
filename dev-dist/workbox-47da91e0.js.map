{"version": 3, "file": "workbox-47da91e0.js", "sources": ["node_modules/workbox-core/_version.js", "node_modules/workbox-core/_private/logger.js", "node_modules/workbox-core/models/messages/messages.js", "node_modules/workbox-core/models/messages/messageGenerator.js", "node_modules/workbox-core/_private/WorkboxError.js", "node_modules/workbox-core/_private/assert.js", "node_modules/workbox-routing/_version.js", "node_modules/workbox-routing/utils/constants.js", "node_modules/workbox-routing/utils/normalizeHandler.js", "node_modules/workbox-routing/Route.js", "node_modules/workbox-routing/RegExpRoute.js", "node_modules/workbox-core/_private/getFriendlyURL.js", "node_modules/workbox-routing/Router.js", "node_modules/workbox-routing/utils/getOrCreateDefaultRouter.js", "node_modules/workbox-routing/registerRoute.js", "node_modules/workbox-core/_private/cacheNames.js", "node_modules/workbox-core/_private/dontWaitFor.js", "node_modules/workbox-core/models/quotaErrorCallbacks.js", "node_modules/workbox-core/registerQuotaErrorCallback.js", "node_modules/idb/build/wrap-idb-value.js", "node_modules/idb/build/index.js", "node_modules/workbox-expiration/_version.js", "node_modules/workbox-expiration/models/CacheTimestampsModel.js", "node_modules/workbox-expiration/CacheExpiration.js", "node_modules/workbox-expiration/ExpirationPlugin.js", "node_modules/workbox-cacheable-response/_version.js", "node_modules/workbox-cacheable-response/CacheableResponse.js", "node_modules/workbox-cacheable-response/CacheableResponsePlugin.js", "node_modules/workbox-core/_private/cacheMatchIgnoreParams.js", "node_modules/workbox-core/_private/Deferred.js", "node_modules/workbox-core/_private/executeQuotaErrorCallbacks.js", "node_modules/workbox-core/_private/timeout.js", "node_modules/workbox-strategies/_version.js", "node_modules/workbox-strategies/StrategyHandler.js", "node_modules/workbox-strategies/Strategy.js", "node_modules/workbox-strategies/utils/messages.js", "node_modules/workbox-strategies/CacheFirst.js", "node_modules/workbox-strategies/plugins/cacheOkAndOpaquePlugin.js", "node_modules/workbox-strategies/NetworkFirst.js", "node_modules/workbox-core/clientsClaim.js", "node_modules/workbox-core/_private/waitUntil.js", "node_modules/workbox-precaching/_version.js", "node_modules/workbox-precaching/utils/createCacheKey.js", "node_modules/workbox-precaching/utils/PrecacheInstallReportPlugin.js", "node_modules/workbox-precaching/utils/PrecacheCacheKeyPlugin.js", "node_modules/workbox-precaching/utils/printCleanupDetails.js", "node_modules/workbox-precaching/utils/printInstallDetails.js", "node_modules/workbox-core/_private/canConstructResponseFromBodyStream.js", "node_modules/workbox-core/copyResponse.js", "node_modules/workbox-precaching/PrecacheStrategy.js", "node_modules/workbox-precaching/PrecacheController.js", "node_modules/workbox-precaching/utils/getOrCreatePrecacheController.js", "node_modules/workbox-precaching/utils/removeIgnoredSearchParams.js", "node_modules/workbox-precaching/utils/generateURLVariations.js", "node_modules/workbox-precaching/PrecacheRoute.js", "node_modules/workbox-precaching/addRoute.js", "node_modules/workbox-precaching/precache.js", "node_modules/workbox-precaching/precacheAndRoute.js", "node_modules/workbox-precaching/utils/deleteOutdatedCaches.js", "node_modules/workbox-precaching/cleanupOutdatedCaches.js", "node_modules/workbox-routing/NavigationRoute.js", "node_modules/workbox-precaching/createHandlerBoundToURL.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:core:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst logger = (process.env.NODE_ENV === 'production'\n    ? null\n    : (() => {\n        // Don't overwrite this value if it's already set.\n        // See https://github.com/GoogleChrome/workbox/pull/2284#issuecomment-*********\n        if (!('__WB_DISABLE_DEV_LOGS' in globalThis)) {\n            self.__WB_DISABLE_DEV_LOGS = false;\n        }\n        let inGroup = false;\n        const methodToColorMap = {\n            debug: `#7f8c8d`,\n            log: `#2ecc71`,\n            warn: `#f39c12`,\n            error: `#c0392b`,\n            groupCollapsed: `#3498db`,\n            groupEnd: null, // No colored prefix on groupEnd\n        };\n        const print = function (method, args) {\n            if (self.__WB_DISABLE_DEV_LOGS) {\n                return;\n            }\n            if (method === 'groupCollapsed') {\n                // Safari doesn't print all console.groupCollapsed() arguments:\n                // https://bugs.webkit.org/show_bug.cgi?id=182754\n                if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n                    console[method](...args);\n                    return;\n                }\n            }\n            const styles = [\n                `background: ${methodToColorMap[method]}`,\n                `border-radius: 0.5em`,\n                `color: white`,\n                `font-weight: bold`,\n                `padding: 2px 0.5em`,\n            ];\n            // When in a group, the workbox prefix is not displayed.\n            const logPrefix = inGroup ? [] : ['%cworkbox', styles.join(';')];\n            console[method](...logPrefix, ...args);\n            if (method === 'groupCollapsed') {\n                inGroup = true;\n            }\n            if (method === 'groupEnd') {\n                inGroup = false;\n            }\n        };\n        // eslint-disable-next-line @typescript-eslint/ban-types\n        const api = {};\n        const loggerMethods = Object.keys(methodToColorMap);\n        for (const key of loggerMethods) {\n            const method = key;\n            api[method] = (...args) => {\n                print(method, args);\n            };\n        }\n        return api;\n    })());\nexport { logger };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../../_version.js';\nexport const messages = {\n    'invalid-value': ({ paramName, validValueDescription, value }) => {\n        if (!paramName || !validValueDescription) {\n            throw new Error(`Unexpected input to 'invalid-value' error.`);\n        }\n        return (`The '${paramName}' parameter was given a value with an ` +\n            `unexpected value. ${validValueDescription} Received a value of ` +\n            `${JSON.stringify(value)}.`);\n    },\n    'not-an-array': ({ moduleName, className, funcName, paramName }) => {\n        if (!moduleName || !className || !funcName || !paramName) {\n            throw new Error(`Unexpected input to 'not-an-array' error.`);\n        }\n        return (`The parameter '${paramName}' passed into ` +\n            `'${moduleName}.${className}.${funcName}()' must be an array.`);\n    },\n    'incorrect-type': ({ expectedType, paramName, moduleName, className, funcName, }) => {\n        if (!expectedType || !paramName || !moduleName || !funcName) {\n            throw new Error(`Unexpected input to 'incorrect-type' error.`);\n        }\n        const classNameStr = className ? `${className}.` : '';\n        return (`The parameter '${paramName}' passed into ` +\n            `'${moduleName}.${classNameStr}` +\n            `${funcName}()' must be of type ${expectedType}.`);\n    },\n    'incorrect-class': ({ expectedClassName, paramName, moduleName, className, funcName, isReturnValueProblem, }) => {\n        if (!expectedClassName || !moduleName || !funcName) {\n            throw new Error(`Unexpected input to 'incorrect-class' error.`);\n        }\n        const classNameStr = className ? `${className}.` : '';\n        if (isReturnValueProblem) {\n            return (`The return value from ` +\n                `'${moduleName}.${classNameStr}${funcName}()' ` +\n                `must be an instance of class ${expectedClassName}.`);\n        }\n        return (`The parameter '${paramName}' passed into ` +\n            `'${moduleName}.${classNameStr}${funcName}()' ` +\n            `must be an instance of class ${expectedClassName}.`);\n    },\n    'missing-a-method': ({ expectedMethod, paramName, moduleName, className, funcName, }) => {\n        if (!expectedMethod ||\n            !paramName ||\n            !moduleName ||\n            !className ||\n            !funcName) {\n            throw new Error(`Unexpected input to 'missing-a-method' error.`);\n        }\n        return (`${moduleName}.${className}.${funcName}() expected the ` +\n            `'${paramName}' parameter to expose a '${expectedMethod}' method.`);\n    },\n    'add-to-cache-list-unexpected-type': ({ entry }) => {\n        return (`An unexpected entry was passed to ` +\n            `'workbox-precaching.PrecacheController.addToCacheList()' The entry ` +\n            `'${JSON.stringify(entry)}' isn't supported. You must supply an array of ` +\n            `strings with one or more characters, objects with a url property or ` +\n            `Request objects.`);\n    },\n    'add-to-cache-list-conflicting-entries': ({ firstEntry, secondEntry }) => {\n        if (!firstEntry || !secondEntry) {\n            throw new Error(`Unexpected input to ` + `'add-to-cache-list-duplicate-entries' error.`);\n        }\n        return (`Two of the entries passed to ` +\n            `'workbox-precaching.PrecacheController.addToCacheList()' had the URL ` +\n            `${firstEntry} but different revision details. Workbox is ` +\n            `unable to cache and version the asset correctly. Please remove one ` +\n            `of the entries.`);\n    },\n    'plugin-error-request-will-fetch': ({ thrownErrorMessage }) => {\n        if (!thrownErrorMessage) {\n            throw new Error(`Unexpected input to ` + `'plugin-error-request-will-fetch', error.`);\n        }\n        return (`An error was thrown by a plugins 'requestWillFetch()' method. ` +\n            `The thrown error message was: '${thrownErrorMessage}'.`);\n    },\n    'invalid-cache-name': ({ cacheNameId, value }) => {\n        if (!cacheNameId) {\n            throw new Error(`Expected a 'cacheNameId' for error 'invalid-cache-name'`);\n        }\n        return (`You must provide a name containing at least one character for ` +\n            `setCacheDetails({${cacheNameId}: '...'}). Received a value of ` +\n            `'${JSON.stringify(value)}'`);\n    },\n    'unregister-route-but-not-found-with-method': ({ method }) => {\n        if (!method) {\n            throw new Error(`Unexpected input to ` +\n                `'unregister-route-but-not-found-with-method' error.`);\n        }\n        return (`The route you're trying to unregister was not  previously ` +\n            `registered for the method type '${method}'.`);\n    },\n    'unregister-route-route-not-registered': () => {\n        return (`The route you're trying to unregister was not previously ` +\n            `registered.`);\n    },\n    'queue-replay-failed': ({ name }) => {\n        return `Replaying the background sync queue '${name}' failed.`;\n    },\n    'duplicate-queue-name': ({ name }) => {\n        return (`The Queue name '${name}' is already being used. ` +\n            `All instances of backgroundSync.Queue must be given unique names.`);\n    },\n    'expired-test-without-max-age': ({ methodName, paramName }) => {\n        return (`The '${methodName}()' method can only be used when the ` +\n            `'${paramName}' is used in the constructor.`);\n    },\n    'unsupported-route-type': ({ moduleName, className, funcName, paramName }) => {\n        return (`The supplied '${paramName}' parameter was an unsupported type. ` +\n            `Please check the docs for ${moduleName}.${className}.${funcName} for ` +\n            `valid input types.`);\n    },\n    'not-array-of-class': ({ value, expectedClass, moduleName, className, funcName, paramName, }) => {\n        return (`The supplied '${paramName}' parameter must be an array of ` +\n            `'${expectedClass}' objects. Received '${JSON.stringify(value)},'. ` +\n            `Please check the call to ${moduleName}.${className}.${funcName}() ` +\n            `to fix the issue.`);\n    },\n    'max-entries-or-age-required': ({ moduleName, className, funcName }) => {\n        return (`You must define either config.maxEntries or config.maxAgeSeconds` +\n            `in ${moduleName}.${className}.${funcName}`);\n    },\n    'statuses-or-headers-required': ({ moduleName, className, funcName }) => {\n        return (`You must define either config.statuses or config.headers` +\n            `in ${moduleName}.${className}.${funcName}`);\n    },\n    'invalid-string': ({ moduleName, funcName, paramName }) => {\n        if (!paramName || !moduleName || !funcName) {\n            throw new Error(`Unexpected input to 'invalid-string' error.`);\n        }\n        return (`When using strings, the '${paramName}' parameter must start with ` +\n            `'http' (for cross-origin matches) or '/' (for same-origin matches). ` +\n            `Please see the docs for ${moduleName}.${funcName}() for ` +\n            `more info.`);\n    },\n    'channel-name-required': () => {\n        return (`You must provide a channelName to construct a ` +\n            `BroadcastCacheUpdate instance.`);\n    },\n    'invalid-responses-are-same-args': () => {\n        return (`The arguments passed into responsesAreSame() appear to be ` +\n            `invalid. Please ensure valid Responses are used.`);\n    },\n    'expire-custom-caches-only': () => {\n        return (`You must provide a 'cacheName' property when using the ` +\n            `expiration plugin with a runtime caching strategy.`);\n    },\n    'unit-must-be-bytes': ({ normalizedRangeHeader }) => {\n        if (!normalizedRangeHeader) {\n            throw new Error(`Unexpected input to 'unit-must-be-bytes' error.`);\n        }\n        return (`The 'unit' portion of the Range header must be set to 'bytes'. ` +\n            `The Range header provided was \"${normalizedRangeHeader}\"`);\n    },\n    'single-range-only': ({ normalizedRangeHeader }) => {\n        if (!normalizedRangeHeader) {\n            throw new Error(`Unexpected input to 'single-range-only' error.`);\n        }\n        return (`Multiple ranges are not supported. Please use a  single start ` +\n            `value, and optional end value. The Range header provided was ` +\n            `\"${normalizedRangeHeader}\"`);\n    },\n    'invalid-range-values': ({ normalizedRangeHeader }) => {\n        if (!normalizedRangeHeader) {\n            throw new Error(`Unexpected input to 'invalid-range-values' error.`);\n        }\n        return (`The Range header is missing both start and end values. At least ` +\n            `one of those values is needed. The Range header provided was ` +\n            `\"${normalizedRangeHeader}\"`);\n    },\n    'no-range-header': () => {\n        return `No Range header was found in the Request provided.`;\n    },\n    'range-not-satisfiable': ({ size, start, end }) => {\n        return (`The start (${start}) and end (${end}) values in the Range are ` +\n            `not satisfiable by the cached response, which is ${size} bytes.`);\n    },\n    'attempt-to-cache-non-get-request': ({ url, method }) => {\n        return (`Unable to cache '${url}' because it is a '${method}' request and ` +\n            `only 'GET' requests can be cached.`);\n    },\n    'cache-put-with-no-response': ({ url }) => {\n        return (`There was an attempt to cache '${url}' but the response was not ` +\n            `defined.`);\n    },\n    'no-response': ({ url, error }) => {\n        let message = `The strategy could not generate a response for '${url}'.`;\n        if (error) {\n            message += ` The underlying error is ${error}.`;\n        }\n        return message;\n    },\n    'bad-precaching-response': ({ url, status }) => {\n        return (`The precaching request for '${url}' failed` +\n            (status ? ` with an HTTP status of ${status}.` : `.`));\n    },\n    'non-precached-url': ({ url }) => {\n        return (`createHandlerBoundToURL('${url}') was called, but that URL is ` +\n            `not precached. Please pass in a URL that is precached instead.`);\n    },\n    'add-to-cache-list-conflicting-integrities': ({ url }) => {\n        return (`Two of the entries passed to ` +\n            `'workbox-precaching.PrecacheController.addToCacheList()' had the URL ` +\n            `${url} with different integrity values. Please remove one of them.`);\n    },\n    'missing-precache-entry': ({ cacheName, url }) => {\n        return `Unable to find a precached response in ${cacheName} for ${url}.`;\n    },\n    'cross-origin-copy-response': ({ origin }) => {\n        return (`workbox-core.copyResponse() can only be used with same-origin ` +\n            `responses. It was passed a response with origin ${origin}.`);\n    },\n    'opaque-streams-source': ({ type }) => {\n        const message = `One of the workbox-streams sources resulted in an ` +\n            `'${type}' response.`;\n        if (type === 'opaqueredirect') {\n            return (`${message} Please do not use a navigation request that results ` +\n                `in a redirect as a source.`);\n        }\n        return `${message} Please ensure your sources are CORS-enabled.`;\n    },\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { messages } from './messages.js';\nimport '../../_version.js';\nconst fallback = (code, ...args) => {\n    let msg = code;\n    if (args.length > 0) {\n        msg += ` :: ${JSON.stringify(args)}`;\n    }\n    return msg;\n};\nconst generatorFunction = (code, details = {}) => {\n    const message = messages[code];\n    if (!message) {\n        throw new Error(`Unable to find message for code '${code}'.`);\n    }\n    return message(details);\n};\nexport const messageGenerator = process.env.NODE_ENV === 'production' ? fallback : generatorFunction;\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { messageGenerator } from '../models/messages/messageGenerator.js';\nimport '../_version.js';\n/**\n * Workbox errors should be thrown with this class.\n * This allows use to ensure the type easily in tests,\n * helps developers identify errors from workbox\n * easily and allows use to optimise error\n * messages correctly.\n *\n * @private\n */\nclass WorkboxError extends Error {\n    /**\n     *\n     * @param {string} errorCode The error code that\n     * identifies this particular error.\n     * @param {Object=} details Any relevant arguments\n     * that will help developers identify issues should\n     * be added as a key on the context object.\n     */\n    constructor(errorCode, details) {\n        const message = messageGenerator(errorCode, details);\n        super(message);\n        this.name = errorCode;\n        this.details = details;\n    }\n}\nexport { WorkboxError };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from '../_private/WorkboxError.js';\nimport '../_version.js';\n/*\n * This method throws if the supplied value is not an array.\n * The destructed values are required to produce a meaningful error for users.\n * The destructed and restructured object is so it's clear what is\n * needed.\n */\nconst isArray = (value, details) => {\n    if (!Array.isArray(value)) {\n        throw new WorkboxError('not-an-array', details);\n    }\n};\nconst hasMethod = (object, expectedMethod, details) => {\n    const type = typeof object[expectedMethod];\n    if (type !== 'function') {\n        details['expectedMethod'] = expectedMethod;\n        throw new WorkboxError('missing-a-method', details);\n    }\n};\nconst isType = (object, expectedType, details) => {\n    if (typeof object !== expectedType) {\n        details['expectedType'] = expectedType;\n        throw new WorkboxError('incorrect-type', details);\n    }\n};\nconst isInstance = (object, \n// Need the general type to do the check later.\n// eslint-disable-next-line @typescript-eslint/ban-types\nexpectedClass, details) => {\n    if (!(object instanceof expectedClass)) {\n        details['expectedClassName'] = expectedClass.name;\n        throw new WorkboxError('incorrect-class', details);\n    }\n};\nconst isOneOf = (value, validValues, details) => {\n    if (!validValues.includes(value)) {\n        details['validValueDescription'] = `Valid values are ${JSON.stringify(validValues)}.`;\n        throw new WorkboxError('invalid-value', details);\n    }\n};\nconst isArrayOfClass = (value, \n// Need general type to do check later.\nexpectedClass, // eslint-disable-line\ndetails) => {\n    const error = new WorkboxError('not-array-of-class', details);\n    if (!Array.isArray(value)) {\n        throw error;\n    }\n    for (const item of value) {\n        if (!(item instanceof expectedClass)) {\n            throw error;\n        }\n    }\n};\nconst finalAssertExports = process.env.NODE_ENV === 'production'\n    ? null\n    : {\n        hasMethod,\n        isArray,\n        isInstance,\n        isOneOf,\n        isType,\n        isArrayOfClass,\n    };\nexport { finalAssertExports as assert };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:routing:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The default HTTP method, 'GET', used when there's no specific method\n * configured for a route.\n *\n * @type {string}\n *\n * @private\n */\nexport const defaultMethod = 'GET';\n/**\n * The list of valid HTTP methods associated with requests that could be routed.\n *\n * @type {Array<string>}\n *\n * @private\n */\nexport const validMethods = [\n    'DELETE',\n    'GET',\n    'HEAD',\n    'PATCH',\n    'POST',\n    'PUT',\n];\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport '../_version.js';\n/**\n * @param {function()|Object} handler Either a function, or an object with a\n * 'handle' method.\n * @return {Object} An object with a handle method.\n *\n * @private\n */\nexport const normalizeHandler = (handler) => {\n    if (handler && typeof handler === 'object') {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.hasMethod(handler, 'handle', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'handler',\n            });\n        }\n        return handler;\n    }\n    else {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(handler, 'function', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'handler',\n            });\n        }\n        return { handle: handler };\n    }\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { defaultMethod, validMethods } from './utils/constants.js';\nimport { normalizeHandler } from './utils/normalizeHandler.js';\nimport './_version.js';\n/**\n * A `Route` consists of a pair of callback functions, \"match\" and \"handler\".\n * The \"match\" callback determine if a route should be used to \"handle\" a\n * request by returning a non-falsy value if it can. The \"handler\" callback\n * is called when there is a match and should return a Promise that resolves\n * to a `Response`.\n *\n * @memberof workbox-routing\n */\nclass Route {\n    /**\n     * Constructor for Route class.\n     *\n     * @param {workbox-routing~matchCallback} match\n     * A callback function that determines whether the route matches a given\n     * `fetch` event by returning a non-falsy value.\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resolving to a Response.\n     * @param {string} [method='GET'] The HTTP method to match the Route\n     * against.\n     */\n    constructor(match, handler, method = defaultMethod) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(match, 'function', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'match',\n            });\n            if (method) {\n                assert.isOneOf(method, validMethods, { paramName: 'method' });\n            }\n        }\n        // These values are referenced directly by Router so cannot be\n        // altered by minificaton.\n        this.handler = normalizeHandler(handler);\n        this.match = match;\n        this.method = method;\n    }\n    /**\n     *\n     * @param {workbox-routing-handlerCallback} handler A callback\n     * function that returns a Promise resolving to a Response\n     */\n    setCatchHandler(handler) {\n        this.catchHandler = normalizeHandler(handler);\n    }\n}\nexport { Route };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { Route } from './Route.js';\nimport './_version.js';\n/**\n * RegExpRoute makes it easy to create a regular expression based\n * {@link workbox-routing.Route}.\n *\n * For same-origin requests the RegExp only needs to match part of the URL. For\n * requests against third-party servers, you must define a RegExp that matches\n * the start of the URL.\n *\n * @memberof workbox-routing\n * @extends workbox-routing.Route\n */\nclass RegExpRoute extends Route {\n    /**\n     * If the regular expression contains\n     * [capture groups]{@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp#grouping-back-references},\n     * the captured values will be passed to the\n     * {@link workbox-routing~handlerCallback} `params`\n     * argument.\n     *\n     * @param {RegExp} regExp The regular expression to match against URLs.\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {string} [method='GET'] The HTTP method to match the Route\n     * against.\n     */\n    constructor(regExp, handler, method) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(regExp, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'RegExpRoute',\n                funcName: 'constructor',\n                paramName: 'pattern',\n            });\n        }\n        const match = ({ url }) => {\n            const result = regExp.exec(url.href);\n            // Return immediately if there's no match.\n            if (!result) {\n                return;\n            }\n            // Require that the match start at the first character in the URL string\n            // if it's a cross-origin request.\n            // See https://github.com/GoogleChrome/workbox/issues/281 for the context\n            // behind this behavior.\n            if (url.origin !== location.origin && result.index !== 0) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.debug(`The regular expression '${regExp.toString()}' only partially matched ` +\n                        `against the cross-origin URL '${url.toString()}'. RegExpRoute's will only ` +\n                        `handle cross-origin requests if they match the entire URL.`);\n                }\n                return;\n            }\n            // If the route matches, but there aren't any capture groups defined, then\n            // this will return [], which is truthy and therefore sufficient to\n            // indicate a match.\n            // If there are capture groups, then it will return their values.\n            return result.slice(1);\n        };\n        super(match, handler, method);\n    }\n}\nexport { RegExpRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst getFriendlyURL = (url) => {\n    const urlObj = new URL(String(url), location.href);\n    // See https://github.com/GoogleChrome/workbox/issues/2323\n    // We want to include everything, except for the origin if it's same-origin.\n    return urlObj.href.replace(new RegExp(`^${location.origin}`), '');\n};\nexport { getFriendlyURL };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { defaultMethod } from './utils/constants.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { normalizeHandler } from './utils/normalizeHandler.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport './_version.js';\n/**\n * The Router can be used to process a `FetchEvent` using one or more\n * {@link workbox-routing.Route}, responding with a `Response` if\n * a matching route exists.\n *\n * If no route matches a given a request, the Router will use a \"default\"\n * handler if one is defined.\n *\n * Should the matching Route throw an error, the Router will use a \"catch\"\n * handler if one is defined to gracefully deal with issues and respond with a\n * Request.\n *\n * If a request matches multiple routes, the **earliest** registered route will\n * be used to respond to the request.\n *\n * @memberof workbox-routing\n */\nclass Router {\n    /**\n     * Initializes a new Router.\n     */\n    constructor() {\n        this._routes = new Map();\n        this._defaultHandlerMap = new Map();\n    }\n    /**\n     * @return {Map<string, Array<workbox-routing.Route>>} routes A `Map` of HTTP\n     * method name ('GET', etc.) to an array of all the corresponding `Route`\n     * instances that are registered.\n     */\n    get routes() {\n        return this._routes;\n    }\n    /**\n     * Adds a fetch event listener to respond to events when a route matches\n     * the event's request.\n     */\n    addFetchListener() {\n        // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n        self.addEventListener('fetch', ((event) => {\n            const { request } = event;\n            const responsePromise = this.handleRequest({ request, event });\n            if (responsePromise) {\n                event.respondWith(responsePromise);\n            }\n        }));\n    }\n    /**\n     * Adds a message event listener for URLs to cache from the window.\n     * This is useful to cache resources loaded on the page prior to when the\n     * service worker started controlling it.\n     *\n     * The format of the message data sent from the window should be as follows.\n     * Where the `urlsToCache` array may consist of URL strings or an array of\n     * URL string + `requestInit` object (the same as you'd pass to `fetch()`).\n     *\n     * ```\n     * {\n     *   type: 'CACHE_URLS',\n     *   payload: {\n     *     urlsToCache: [\n     *       './script1.js',\n     *       './script2.js',\n     *       ['./script3.js', {mode: 'no-cors'}],\n     *     ],\n     *   },\n     * }\n     * ```\n     */\n    addCacheListener() {\n        // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n        self.addEventListener('message', ((event) => {\n            // event.data is type 'any'\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n            if (event.data && event.data.type === 'CACHE_URLS') {\n                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                const { payload } = event.data;\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.debug(`Caching URLs from the window`, payload.urlsToCache);\n                }\n                const requestPromises = Promise.all(payload.urlsToCache.map((entry) => {\n                    if (typeof entry === 'string') {\n                        entry = [entry];\n                    }\n                    const request = new Request(...entry);\n                    return this.handleRequest({ request, event });\n                    // TODO(philipwalton): TypeScript errors without this typecast for\n                    // some reason (probably a bug). The real type here should work but\n                    // doesn't: `Array<Promise<Response> | undefined>`.\n                })); // TypeScript\n                event.waitUntil(requestPromises);\n                // If a MessageChannel was used, reply to the message on success.\n                if (event.ports && event.ports[0]) {\n                    void requestPromises.then(() => event.ports[0].postMessage(true));\n                }\n            }\n        }));\n    }\n    /**\n     * Apply the routing rules to a FetchEvent object to get a Response from an\n     * appropriate Route's handler.\n     *\n     * @param {Object} options\n     * @param {Request} options.request The request to handle.\n     * @param {ExtendableEvent} options.event The event that triggered the\n     *     request.\n     * @return {Promise<Response>|undefined} A promise is returned if a\n     *     registered route can handle the request. If there is no matching\n     *     route and there's no `defaultHandler`, `undefined` is returned.\n     */\n    handleRequest({ request, event, }) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'handleRequest',\n                paramName: 'options.request',\n            });\n        }\n        const url = new URL(request.url, location.href);\n        if (!url.protocol.startsWith('http')) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Workbox Router only supports URLs that start with 'http'.`);\n            }\n            return;\n        }\n        const sameOrigin = url.origin === location.origin;\n        const { params, route } = this.findMatchingRoute({\n            event,\n            request,\n            sameOrigin,\n            url,\n        });\n        let handler = route && route.handler;\n        const debugMessages = [];\n        if (process.env.NODE_ENV !== 'production') {\n            if (handler) {\n                debugMessages.push([`Found a route to handle this request:`, route]);\n                if (params) {\n                    debugMessages.push([\n                        `Passing the following params to the route's handler:`,\n                        params,\n                    ]);\n                }\n            }\n        }\n        // If we don't have a handler because there was no matching route, then\n        // fall back to defaultHandler if that's defined.\n        const method = request.method;\n        if (!handler && this._defaultHandlerMap.has(method)) {\n            if (process.env.NODE_ENV !== 'production') {\n                debugMessages.push(`Failed to find a matching route. Falling ` +\n                    `back to the default handler for ${method}.`);\n            }\n            handler = this._defaultHandlerMap.get(method);\n        }\n        if (!handler) {\n            if (process.env.NODE_ENV !== 'production') {\n                // No handler so Workbox will do nothing. If logs is set of debug\n                // i.e. verbose, we should print out this information.\n                logger.debug(`No route found for: ${getFriendlyURL(url)}`);\n            }\n            return;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            // We have a handler, meaning Workbox is going to handle the route.\n            // print the routing details to the console.\n            logger.groupCollapsed(`Router is responding to: ${getFriendlyURL(url)}`);\n            debugMessages.forEach((msg) => {\n                if (Array.isArray(msg)) {\n                    logger.log(...msg);\n                }\n                else {\n                    logger.log(msg);\n                }\n            });\n            logger.groupEnd();\n        }\n        // Wrap in try and catch in case the handle method throws a synchronous\n        // error. It should still callback to the catch handler.\n        let responsePromise;\n        try {\n            responsePromise = handler.handle({ url, request, event, params });\n        }\n        catch (err) {\n            responsePromise = Promise.reject(err);\n        }\n        // Get route's catch handler, if it exists\n        const catchHandler = route && route.catchHandler;\n        if (responsePromise instanceof Promise &&\n            (this._catchHandler || catchHandler)) {\n            responsePromise = responsePromise.catch(async (err) => {\n                // If there's a route catch handler, process that first\n                if (catchHandler) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // Still include URL here as it will be async from the console group\n                        // and may not make sense without the URL\n                        logger.groupCollapsed(`Error thrown when responding to: ` +\n                            ` ${getFriendlyURL(url)}. Falling back to route's Catch Handler.`);\n                        logger.error(`Error thrown by:`, route);\n                        logger.error(err);\n                        logger.groupEnd();\n                    }\n                    try {\n                        return await catchHandler.handle({ url, request, event, params });\n                    }\n                    catch (catchErr) {\n                        if (catchErr instanceof Error) {\n                            err = catchErr;\n                        }\n                    }\n                }\n                if (this._catchHandler) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // Still include URL here as it will be async from the console group\n                        // and may not make sense without the URL\n                        logger.groupCollapsed(`Error thrown when responding to: ` +\n                            ` ${getFriendlyURL(url)}. Falling back to global Catch Handler.`);\n                        logger.error(`Error thrown by:`, route);\n                        logger.error(err);\n                        logger.groupEnd();\n                    }\n                    return this._catchHandler.handle({ url, request, event });\n                }\n                throw err;\n            });\n        }\n        return responsePromise;\n    }\n    /**\n     * Checks a request and URL (and optionally an event) against the list of\n     * registered routes, and if there's a match, returns the corresponding\n     * route along with any params generated by the match.\n     *\n     * @param {Object} options\n     * @param {URL} options.url\n     * @param {boolean} options.sameOrigin The result of comparing `url.origin`\n     *     against the current origin.\n     * @param {Request} options.request The request to match.\n     * @param {Event} options.event The corresponding event.\n     * @return {Object} An object with `route` and `params` properties.\n     *     They are populated if a matching route was found or `undefined`\n     *     otherwise.\n     */\n    findMatchingRoute({ url, sameOrigin, request, event, }) {\n        const routes = this._routes.get(request.method) || [];\n        for (const route of routes) {\n            let params;\n            // route.match returns type any, not possible to change right now.\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            const matchResult = route.match({ url, sameOrigin, request, event });\n            if (matchResult) {\n                if (process.env.NODE_ENV !== 'production') {\n                    // Warn developers that using an async matchCallback is almost always\n                    // not the right thing to do.\n                    if (matchResult instanceof Promise) {\n                        logger.warn(`While routing ${getFriendlyURL(url)}, an async ` +\n                            `matchCallback function was used. Please convert the ` +\n                            `following route to use a synchronous matchCallback function:`, route);\n                    }\n                }\n                // See https://github.com/GoogleChrome/workbox/issues/2079\n                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                params = matchResult;\n                if (Array.isArray(params) && params.length === 0) {\n                    // Instead of passing an empty array in as params, use undefined.\n                    params = undefined;\n                }\n                else if (matchResult.constructor === Object && // eslint-disable-line\n                    Object.keys(matchResult).length === 0) {\n                    // Instead of passing an empty object in as params, use undefined.\n                    params = undefined;\n                }\n                else if (typeof matchResult === 'boolean') {\n                    // For the boolean value true (rather than just something truth-y),\n                    // don't set params.\n                    // See https://github.com/GoogleChrome/workbox/pull/2134#issuecomment-513924353\n                    params = undefined;\n                }\n                // Return early if have a match.\n                return { route, params };\n            }\n        }\n        // If no match was found above, return and empty object.\n        return {};\n    }\n    /**\n     * Define a default `handler` that's called when no routes explicitly\n     * match the incoming request.\n     *\n     * Each HTTP method ('GET', 'POST', etc.) gets its own default handler.\n     *\n     * Without a default handler, unmatched requests will go against the\n     * network as if there were no service worker present.\n     *\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {string} [method='GET'] The HTTP method to associate with this\n     * default handler. Each method has its own default.\n     */\n    setDefaultHandler(handler, method = defaultMethod) {\n        this._defaultHandlerMap.set(method, normalizeHandler(handler));\n    }\n    /**\n     * If a Route throws an error while handling a request, this `handler`\n     * will be called and given a chance to provide a response.\n     *\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     */\n    setCatchHandler(handler) {\n        this._catchHandler = normalizeHandler(handler);\n    }\n    /**\n     * Registers a route with the router.\n     *\n     * @param {workbox-routing.Route} route The route to register.\n     */\n    registerRoute(route) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(route, 'object', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.hasMethod(route, 'match', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.isType(route.handler, 'object', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.hasMethod(route.handler, 'handle', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route.handler',\n            });\n            assert.isType(route.method, 'string', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route.method',\n            });\n        }\n        if (!this._routes.has(route.method)) {\n            this._routes.set(route.method, []);\n        }\n        // Give precedence to all of the earlier routes by adding this additional\n        // route to the end of the array.\n        this._routes.get(route.method).push(route);\n    }\n    /**\n     * Unregisters a route with the router.\n     *\n     * @param {workbox-routing.Route} route The route to unregister.\n     */\n    unregisterRoute(route) {\n        if (!this._routes.has(route.method)) {\n            throw new WorkboxError('unregister-route-but-not-found-with-method', {\n                method: route.method,\n            });\n        }\n        const routeIndex = this._routes.get(route.method).indexOf(route);\n        if (routeIndex > -1) {\n            this._routes.get(route.method).splice(routeIndex, 1);\n        }\n        else {\n            throw new WorkboxError('unregister-route-route-not-registered');\n        }\n    }\n}\nexport { Router };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { Router } from '../Router.js';\nimport '../_version.js';\nlet defaultRouter;\n/**\n * Creates a new, singleton Router instance if one does not exist. If one\n * does already exist, that instance is returned.\n *\n * @private\n * @return {Router}\n */\nexport const getOrCreateDefaultRouter = () => {\n    if (!defaultRouter) {\n        defaultRouter = new Router();\n        // The helpers that use the default Router assume these listeners exist.\n        defaultRouter.addFetchListener();\n        defaultRouter.addCacheListener();\n    }\n    return defaultRouter;\n};\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Route } from './Route.js';\nimport { RegExpRoute } from './RegExpRoute.js';\nimport { getOrCreateDefaultRouter } from './utils/getOrCreateDefaultRouter.js';\nimport './_version.js';\n/**\n * Easily register a RegExp, string, or function with a caching\n * strategy to a singleton Router instance.\n *\n * This method will generate a Route for you if needed and\n * call {@link workbox-routing.Router#registerRoute}.\n *\n * @param {RegExp|string|workbox-routing.Route~matchCallback|workbox-routing.Route} capture\n * If the capture param is a `Route`, all other arguments will be ignored.\n * @param {workbox-routing~handlerCallback} [handler] A callback\n * function that returns a Promise resulting in a Response. This parameter\n * is required if `capture` is not a `Route` object.\n * @param {string} [method='GET'] The HTTP method to match the Route\n * against.\n * @return {workbox-routing.Route} The generated `Route`.\n *\n * @memberof workbox-routing\n */\nfunction registerRoute(capture, handler, method) {\n    let route;\n    if (typeof capture === 'string') {\n        const captureUrl = new URL(capture, location.href);\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(capture.startsWith('/') || capture.startsWith('http'))) {\n                throw new WorkboxError('invalid-string', {\n                    moduleName: 'workbox-routing',\n                    funcName: 'registerRoute',\n                    paramName: 'capture',\n                });\n            }\n            // We want to check if Express-style wildcards are in the pathname only.\n            // TODO: Remove this log message in v4.\n            const valueToCheck = capture.startsWith('http')\n                ? captureUrl.pathname\n                : capture;\n            // See https://github.com/pillarjs/path-to-regexp#parameters\n            const wildcards = '[*:?+]';\n            if (new RegExp(`${wildcards}`).exec(valueToCheck)) {\n                logger.debug(`The '$capture' parameter contains an Express-style wildcard ` +\n                    `character (${wildcards}). Strings are now always interpreted as ` +\n                    `exact matches; use a RegExp for partial or wildcard matches.`);\n            }\n        }\n        const matchCallback = ({ url }) => {\n            if (process.env.NODE_ENV !== 'production') {\n                if (url.pathname === captureUrl.pathname &&\n                    url.origin !== captureUrl.origin) {\n                    logger.debug(`${capture} only partially matches the cross-origin URL ` +\n                        `${url.toString()}. This route will only handle cross-origin requests ` +\n                        `if they match the entire URL.`);\n                }\n            }\n            return url.href === captureUrl.href;\n        };\n        // If `capture` is a string then `handler` and `method` must be present.\n        route = new Route(matchCallback, handler, method);\n    }\n    else if (capture instanceof RegExp) {\n        // If `capture` is a `RegExp` then `handler` and `method` must be present.\n        route = new RegExpRoute(capture, handler, method);\n    }\n    else if (typeof capture === 'function') {\n        // If `capture` is a function then `handler` and `method` must be present.\n        route = new Route(capture, handler, method);\n    }\n    else if (capture instanceof Route) {\n        route = capture;\n    }\n    else {\n        throw new WorkboxError('unsupported-route-type', {\n            moduleName: 'workbox-routing',\n            funcName: 'registerRoute',\n            paramName: 'capture',\n        });\n    }\n    const defaultRouter = getOrCreateDefaultRouter();\n    defaultRouter.registerRoute(route);\n    return route;\n}\nexport { registerRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst _cacheNameDetails = {\n    googleAnalytics: 'googleAnalytics',\n    precache: 'precache-v2',\n    prefix: 'workbox',\n    runtime: 'runtime',\n    suffix: typeof registration !== 'undefined' ? registration.scope : '',\n};\nconst _createCacheName = (cacheName) => {\n    return [_cacheNameDetails.prefix, cacheName, _cacheNameDetails.suffix]\n        .filter((value) => value && value.length > 0)\n        .join('-');\n};\nconst eachCacheNameDetail = (fn) => {\n    for (const key of Object.keys(_cacheNameDetails)) {\n        fn(key);\n    }\n};\nexport const cacheNames = {\n    updateDetails: (details) => {\n        eachCacheNameDetail((key) => {\n            if (typeof details[key] === 'string') {\n                _cacheNameDetails[key] = details[key];\n            }\n        });\n    },\n    getGoogleAnalyticsName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.googleAnalytics);\n    },\n    getPrecacheName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.precache);\n    },\n    getPrefix: () => {\n        return _cacheNameDetails.prefix;\n    },\n    getRuntimeName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.runtime);\n    },\n    getSuffix: () => {\n        return _cacheNameDetails.suffix;\n    },\n};\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A helper function that prevents a promise from being flagged as unused.\n *\n * @private\n **/\nexport function dontWaitFor(promise) {\n    // Effective no-op.\n    void promise.then(() => { });\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n// Callbacks to be executed whenever there's a quota error.\n// Can't change Function type right now.\n// eslint-disable-next-line @typescript-eslint/ban-types\nconst quotaErrorCallbacks = new Set();\nexport { quotaErrorCallbacks };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from './_private/logger.js';\nimport { assert } from './_private/assert.js';\nimport { quotaErrorCallbacks } from './models/quotaErrorCallbacks.js';\nimport './_version.js';\n/**\n * Adds a function to the set of quotaErrorCallbacks that will be executed if\n * there's a quota error.\n *\n * @param {Function} callback\n * @memberof workbox-core\n */\n// Can't change Function type\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction registerQuotaErrorCallback(callback) {\n    if (process.env.NODE_ENV !== 'production') {\n        assert.isType(callback, 'function', {\n            moduleName: 'workbox-core',\n            funcName: 'register',\n            paramName: 'callback',\n        });\n    }\n    quotaErrorCallbacks.add(callback);\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Registered a callback to respond to quota errors.', callback);\n    }\n}\nexport { registerQuotaErrorCallback };\n", "const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:expiration:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { openDB, deleteDB } from 'idb';\nimport '../_version.js';\nconst DB_NAME = 'workbox-expiration';\nconst CACHE_OBJECT_STORE = 'cache-entries';\nconst normalizeURL = (unNormalizedUrl) => {\n    const url = new URL(unNormalizedUrl, location.href);\n    url.hash = '';\n    return url.href;\n};\n/**\n * Returns the timestamp model.\n *\n * @private\n */\nclass CacheTimestampsModel {\n    /**\n     *\n     * @param {string} cacheName\n     *\n     * @private\n     */\n    constructor(cacheName) {\n        this._db = null;\n        this._cacheName = cacheName;\n    }\n    /**\n     * Performs an upgrade of indexedDB.\n     *\n     * @param {IDBPDatabase<CacheDbSchema>} db\n     *\n     * @private\n     */\n    _upgradeDb(db) {\n        // TODO(philipwalton): EdgeHTML doesn't support arrays as a keyPath, so we\n        // have to use the `id` keyPath here and create our own values (a\n        // concatenation of `url + cacheName`) instead of simply using\n        // `keyPath: ['url', 'cacheName']`, which is supported in other browsers.\n        const objStore = db.createObjectStore(CACHE_OBJECT_STORE, { keyPath: 'id' });\n        // TODO(philipwalton): once we don't have to support EdgeHTML, we can\n        // create a single index with the keyPath `['cacheName', 'timestamp']`\n        // instead of doing both these indexes.\n        objStore.createIndex('cacheName', 'cacheName', { unique: false });\n        objStore.createIndex('timestamp', 'timestamp', { unique: false });\n    }\n    /**\n     * Performs an upgrade of indexedDB and deletes deprecated DBs.\n     *\n     * @param {IDBPDatabase<CacheDbSchema>} db\n     *\n     * @private\n     */\n    _upgradeDbAndDeleteOldDbs(db) {\n        this._upgradeDb(db);\n        if (this._cacheName) {\n            void deleteDB(this._cacheName);\n        }\n    }\n    /**\n     * @param {string} url\n     * @param {number} timestamp\n     *\n     * @private\n     */\n    async setTimestamp(url, timestamp) {\n        url = normalizeURL(url);\n        const entry = {\n            url,\n            timestamp,\n            cacheName: this._cacheName,\n            // Creating an ID from the URL and cache name won't be necessary once\n            // Edge switches to Chromium and all browsers we support work with\n            // array keyPaths.\n            id: this._getId(url),\n        };\n        const db = await this.getDb();\n        const tx = db.transaction(CACHE_OBJECT_STORE, 'readwrite', {\n            durability: 'relaxed',\n        });\n        await tx.store.put(entry);\n        await tx.done;\n    }\n    /**\n     * Returns the timestamp stored for a given URL.\n     *\n     * @param {string} url\n     * @return {number | undefined}\n     *\n     * @private\n     */\n    async getTimestamp(url) {\n        const db = await this.getDb();\n        const entry = await db.get(CACHE_OBJECT_STORE, this._getId(url));\n        return entry === null || entry === void 0 ? void 0 : entry.timestamp;\n    }\n    /**\n     * Iterates through all the entries in the object store (from newest to\n     * oldest) and removes entries once either `maxCount` is reached or the\n     * entry's timestamp is less than `minTimestamp`.\n     *\n     * @param {number} minTimestamp\n     * @param {number} maxCount\n     * @return {Array<string>}\n     *\n     * @private\n     */\n    async expireEntries(minTimestamp, maxCount) {\n        const db = await this.getDb();\n        let cursor = await db\n            .transaction(CACHE_OBJECT_STORE)\n            .store.index('timestamp')\n            .openCursor(null, 'prev');\n        const entriesToDelete = [];\n        let entriesNotDeletedCount = 0;\n        while (cursor) {\n            const result = cursor.value;\n            // TODO(philipwalton): once we can use a multi-key index, we\n            // won't have to check `cacheName` here.\n            if (result.cacheName === this._cacheName) {\n                // Delete an entry if it's older than the max age or\n                // if we already have the max number allowed.\n                if ((minTimestamp && result.timestamp < minTimestamp) ||\n                    (maxCount && entriesNotDeletedCount >= maxCount)) {\n                    // TODO(philipwalton): we should be able to delete the\n                    // entry right here, but doing so causes an iteration\n                    // bug in Safari stable (fixed in TP). Instead we can\n                    // store the keys of the entries to delete, and then\n                    // delete the separate transactions.\n                    // https://github.com/GoogleChrome/workbox/issues/1978\n                    // cursor.delete();\n                    // We only need to return the URL, not the whole entry.\n                    entriesToDelete.push(cursor.value);\n                }\n                else {\n                    entriesNotDeletedCount++;\n                }\n            }\n            cursor = await cursor.continue();\n        }\n        // TODO(philipwalton): once the Safari bug in the following issue is fixed,\n        // we should be able to remove this loop and do the entry deletion in the\n        // cursor loop above:\n        // https://github.com/GoogleChrome/workbox/issues/1978\n        const urlsDeleted = [];\n        for (const entry of entriesToDelete) {\n            await db.delete(CACHE_OBJECT_STORE, entry.id);\n            urlsDeleted.push(entry.url);\n        }\n        return urlsDeleted;\n    }\n    /**\n     * Takes a URL and returns an ID that will be unique in the object store.\n     *\n     * @param {string} url\n     * @return {string}\n     *\n     * @private\n     */\n    _getId(url) {\n        // Creating an ID from the URL and cache name won't be necessary once\n        // Edge switches to Chromium and all browsers we support work with\n        // array keyPaths.\n        return this._cacheName + '|' + normalizeURL(url);\n    }\n    /**\n     * Returns an open connection to the database.\n     *\n     * @private\n     */\n    async getDb() {\n        if (!this._db) {\n            this._db = await openDB(DB_NAME, 1, {\n                upgrade: this._upgradeDbAndDeleteOldDbs.bind(this),\n            });\n        }\n        return this._db;\n    }\n}\nexport { CacheTimestampsModel };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { CacheTimestampsModel } from './models/CacheTimestampsModel.js';\nimport './_version.js';\n/**\n * The `CacheExpiration` class allows you define an expiration and / or\n * limit on the number of responses stored in a\n * [`Cache`](https://developer.mozilla.org/en-US/docs/Web/API/Cache).\n *\n * @memberof workbox-expiration\n */\nclass CacheExpiration {\n    /**\n     * To construct a new CacheExpiration instance you must provide at least\n     * one of the `config` properties.\n     *\n     * @param {string} cacheName Name of the cache to apply restrictions to.\n     * @param {Object} config\n     * @param {number} [config.maxEntries] The maximum number of entries to cache.\n     * Entries used the least will be removed as the maximum is reached.\n     * @param {number} [config.maxAgeSeconds] The maximum age of an entry before\n     * it's treated as stale and removed.\n     * @param {Object} [config.matchOptions] The [`CacheQueryOptions`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/delete#Parameters)\n     * that will be used when calling `delete()` on the cache.\n     */\n    constructor(cacheName, config = {}) {\n        this._isRunning = false;\n        this._rerunRequested = false;\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(cacheName, 'string', {\n                moduleName: 'workbox-expiration',\n                className: 'CacheExpiration',\n                funcName: 'constructor',\n                paramName: 'cacheName',\n            });\n            if (!(config.maxEntries || config.maxAgeSeconds)) {\n                throw new WorkboxError('max-entries-or-age-required', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.maxEntries) {\n                assert.isType(config.maxEntries, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                    paramName: 'config.maxEntries',\n                });\n            }\n            if (config.maxAgeSeconds) {\n                assert.isType(config.maxAgeSeconds, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                    paramName: 'config.maxAgeSeconds',\n                });\n            }\n        }\n        this._maxEntries = config.maxEntries;\n        this._maxAgeSeconds = config.maxAgeSeconds;\n        this._matchOptions = config.matchOptions;\n        this._cacheName = cacheName;\n        this._timestampModel = new CacheTimestampsModel(cacheName);\n    }\n    /**\n     * Expires entries for the given cache and given criteria.\n     */\n    async expireEntries() {\n        if (this._isRunning) {\n            this._rerunRequested = true;\n            return;\n        }\n        this._isRunning = true;\n        const minTimestamp = this._maxAgeSeconds\n            ? Date.now() - this._maxAgeSeconds * 1000\n            : 0;\n        const urlsExpired = await this._timestampModel.expireEntries(minTimestamp, this._maxEntries);\n        // Delete URLs from the cache\n        const cache = await self.caches.open(this._cacheName);\n        for (const url of urlsExpired) {\n            await cache.delete(url, this._matchOptions);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (urlsExpired.length > 0) {\n                logger.groupCollapsed(`Expired ${urlsExpired.length} ` +\n                    `${urlsExpired.length === 1 ? 'entry' : 'entries'} and removed ` +\n                    `${urlsExpired.length === 1 ? 'it' : 'them'} from the ` +\n                    `'${this._cacheName}' cache.`);\n                logger.log(`Expired the following ${urlsExpired.length === 1 ? 'URL' : 'URLs'}:`);\n                urlsExpired.forEach((url) => logger.log(`    ${url}`));\n                logger.groupEnd();\n            }\n            else {\n                logger.debug(`Cache expiration ran and found no entries to remove.`);\n            }\n        }\n        this._isRunning = false;\n        if (this._rerunRequested) {\n            this._rerunRequested = false;\n            dontWaitFor(this.expireEntries());\n        }\n    }\n    /**\n     * Update the timestamp for the given URL. This ensures the when\n     * removing entries based on maximum entries, most recently used\n     * is accurate or when expiring, the timestamp is up-to-date.\n     *\n     * @param {string} url\n     */\n    async updateTimestamp(url) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(url, 'string', {\n                moduleName: 'workbox-expiration',\n                className: 'CacheExpiration',\n                funcName: 'updateTimestamp',\n                paramName: 'url',\n            });\n        }\n        await this._timestampModel.setTimestamp(url, Date.now());\n    }\n    /**\n     * Can be used to check if a URL has expired or not before it's used.\n     *\n     * This requires a look up from IndexedDB, so can be slow.\n     *\n     * Note: This method will not remove the cached entry, call\n     * `expireEntries()` to remove indexedDB and Cache entries.\n     *\n     * @param {string} url\n     * @return {boolean}\n     */\n    async isURLExpired(url) {\n        if (!this._maxAgeSeconds) {\n            if (process.env.NODE_ENV !== 'production') {\n                throw new WorkboxError(`expired-test-without-max-age`, {\n                    methodName: 'isURLExpired',\n                    paramName: 'maxAgeSeconds',\n                });\n            }\n            return false;\n        }\n        else {\n            const timestamp = await this._timestampModel.getTimestamp(url);\n            const expireOlderThan = Date.now() - this._maxAgeSeconds * 1000;\n            return timestamp !== undefined ? timestamp < expireOlderThan : true;\n        }\n    }\n    /**\n     * Removes the IndexedDB object store used to keep track of cache expiration\n     * metadata.\n     */\n    async delete() {\n        // Make sure we don't attempt another rerun if we're called in the middle of\n        // a cache expiration.\n        this._rerunRequested = false;\n        await this._timestampModel.expireEntries(Infinity); // Expires all.\n    }\n}\nexport { CacheExpiration };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { registerQuotaErrorCallback } from 'workbox-core/registerQuotaErrorCallback.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { CacheExpiration } from './CacheExpiration.js';\nimport './_version.js';\n/**\n * This plugin can be used in a `workbox-strategy` to regularly enforce a\n * limit on the age and / or the number of cached requests.\n *\n * It can only be used with `workbox-strategy` instances that have a\n * [custom `cacheName` property set](/web/tools/workbox/guides/configure-workbox#custom_cache_names_in_strategies).\n * In other words, it can't be used to expire entries in strategy that uses the\n * default runtime cache name.\n *\n * Whenever a cached response is used or updated, this plugin will look\n * at the associated cache and remove any old or extra responses.\n *\n * When using `maxAgeSeconds`, responses may be used *once* after expiring\n * because the expiration clean up will not have occurred until *after* the\n * cached response has been used. If the response has a \"Date\" header, then\n * a light weight expiration check is performed and the response will not be\n * used immediately.\n *\n * When using `maxEntries`, the entry least-recently requested will be removed\n * from the cache first.\n *\n * @memberof workbox-expiration\n */\nclass ExpirationPlugin {\n    /**\n     * @param {ExpirationPluginOptions} config\n     * @param {number} [config.maxEntries] The maximum number of entries to cache.\n     * Entries used the least will be removed as the maximum is reached.\n     * @param {number} [config.maxAgeSeconds] The maximum age of an entry before\n     * it's treated as stale and removed.\n     * @param {Object} [config.matchOptions] The [`CacheQueryOptions`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/delete#Parameters)\n     * that will be used when calling `delete()` on the cache.\n     * @param {boolean} [config.purgeOnQuotaError] Whether to opt this cache in to\n     * automatic deletion if the available storage quota has been exceeded.\n     */\n    constructor(config = {}) {\n        /**\n         * A \"lifecycle\" callback that will be triggered automatically by the\n         * `workbox-strategies` handlers when a `Response` is about to be returned\n         * from a [Cache](https://developer.mozilla.org/en-US/docs/Web/API/Cache) to\n         * the handler. It allows the `Response` to be inspected for freshness and\n         * prevents it from being used if the `Response`'s `Date` header value is\n         * older than the configured `maxAgeSeconds`.\n         *\n         * @param {Object} options\n         * @param {string} options.cacheName Name of the cache the response is in.\n         * @param {Response} options.cachedResponse The `Response` object that's been\n         *     read from a cache and whose freshness should be checked.\n         * @return {Response} Either the `cachedResponse`, if it's\n         *     fresh, or `null` if the `Response` is older than `maxAgeSeconds`.\n         *\n         * @private\n         */\n        this.cachedResponseWillBeUsed = async ({ event, request, cacheName, cachedResponse, }) => {\n            if (!cachedResponse) {\n                return null;\n            }\n            const isFresh = this._isResponseDateFresh(cachedResponse);\n            // Expire entries to ensure that even if the expiration date has\n            // expired, it'll only be used once.\n            const cacheExpiration = this._getCacheExpiration(cacheName);\n            dontWaitFor(cacheExpiration.expireEntries());\n            // Update the metadata for the request URL to the current timestamp,\n            // but don't `await` it as we don't want to block the response.\n            const updateTimestampDone = cacheExpiration.updateTimestamp(request.url);\n            if (event) {\n                try {\n                    event.waitUntil(updateTimestampDone);\n                }\n                catch (error) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // The event may not be a fetch event; only log the URL if it is.\n                        if ('request' in event) {\n                            logger.warn(`Unable to ensure service worker stays alive when ` +\n                                `updating cache entry for ` +\n                                `'${getFriendlyURL(event.request.url)}'.`);\n                        }\n                    }\n                }\n            }\n            return isFresh ? cachedResponse : null;\n        };\n        /**\n         * A \"lifecycle\" callback that will be triggered automatically by the\n         * `workbox-strategies` handlers when an entry is added to a cache.\n         *\n         * @param {Object} options\n         * @param {string} options.cacheName Name of the cache that was updated.\n         * @param {string} options.request The Request for the cached entry.\n         *\n         * @private\n         */\n        this.cacheDidUpdate = async ({ cacheName, request, }) => {\n            if (process.env.NODE_ENV !== 'production') {\n                assert.isType(cacheName, 'string', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'cacheDidUpdate',\n                    paramName: 'cacheName',\n                });\n                assert.isInstance(request, Request, {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'cacheDidUpdate',\n                    paramName: 'request',\n                });\n            }\n            const cacheExpiration = this._getCacheExpiration(cacheName);\n            await cacheExpiration.updateTimestamp(request.url);\n            await cacheExpiration.expireEntries();\n        };\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(config.maxEntries || config.maxAgeSeconds)) {\n                throw new WorkboxError('max-entries-or-age-required', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.maxEntries) {\n                assert.isType(config.maxEntries, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                    paramName: 'config.maxEntries',\n                });\n            }\n            if (config.maxAgeSeconds) {\n                assert.isType(config.maxAgeSeconds, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                    paramName: 'config.maxAgeSeconds',\n                });\n            }\n        }\n        this._config = config;\n        this._maxAgeSeconds = config.maxAgeSeconds;\n        this._cacheExpirations = new Map();\n        if (config.purgeOnQuotaError) {\n            registerQuotaErrorCallback(() => this.deleteCacheAndMetadata());\n        }\n    }\n    /**\n     * A simple helper method to return a CacheExpiration instance for a given\n     * cache name.\n     *\n     * @param {string} cacheName\n     * @return {CacheExpiration}\n     *\n     * @private\n     */\n    _getCacheExpiration(cacheName) {\n        if (cacheName === cacheNames.getRuntimeName()) {\n            throw new WorkboxError('expire-custom-caches-only');\n        }\n        let cacheExpiration = this._cacheExpirations.get(cacheName);\n        if (!cacheExpiration) {\n            cacheExpiration = new CacheExpiration(cacheName, this._config);\n            this._cacheExpirations.set(cacheName, cacheExpiration);\n        }\n        return cacheExpiration;\n    }\n    /**\n     * @param {Response} cachedResponse\n     * @return {boolean}\n     *\n     * @private\n     */\n    _isResponseDateFresh(cachedResponse) {\n        if (!this._maxAgeSeconds) {\n            // We aren't expiring by age, so return true, it's fresh\n            return true;\n        }\n        // Check if the 'date' header will suffice a quick expiration check.\n        // See https://github.com/GoogleChromeLabs/sw-toolbox/issues/164 for\n        // discussion.\n        const dateHeaderTimestamp = this._getDateHeaderTimestamp(cachedResponse);\n        if (dateHeaderTimestamp === null) {\n            // Unable to parse date, so assume it's fresh.\n            return true;\n        }\n        // If we have a valid headerTime, then our response is fresh iff the\n        // headerTime plus maxAgeSeconds is greater than the current time.\n        const now = Date.now();\n        return dateHeaderTimestamp >= now - this._maxAgeSeconds * 1000;\n    }\n    /**\n     * This method will extract the data header and parse it into a useful\n     * value.\n     *\n     * @param {Response} cachedResponse\n     * @return {number|null}\n     *\n     * @private\n     */\n    _getDateHeaderTimestamp(cachedResponse) {\n        if (!cachedResponse.headers.has('date')) {\n            return null;\n        }\n        const dateHeader = cachedResponse.headers.get('date');\n        const parsedDate = new Date(dateHeader);\n        const headerTime = parsedDate.getTime();\n        // If the Date header was invalid for some reason, parsedDate.getTime()\n        // will return NaN.\n        if (isNaN(headerTime)) {\n            return null;\n        }\n        return headerTime;\n    }\n    /**\n     * This is a helper method that performs two operations:\n     *\n     * - Deletes *all* the underlying Cache instances associated with this plugin\n     * instance, by calling caches.delete() on your behalf.\n     * - Deletes the metadata from IndexedDB used to keep track of expiration\n     * details for each Cache instance.\n     *\n     * When using cache expiration, calling this method is preferable to calling\n     * `caches.delete()` directly, since this will ensure that the IndexedDB\n     * metadata is also cleanly removed and open IndexedDB instances are deleted.\n     *\n     * Note that if you're *not* using cache expiration for a given cache, calling\n     * `caches.delete()` and passing in the cache's name should be sufficient.\n     * There is no Workbox-specific method needed for cleanup in that case.\n     */\n    async deleteCacheAndMetadata() {\n        // Do this one at a time instead of all at once via `Promise.all()` to\n        // reduce the chance of inconsistency if a promise rejects.\n        for (const [cacheName, cacheExpiration] of this._cacheExpirations) {\n            await self.caches.delete(cacheName);\n            await cacheExpiration.delete();\n        }\n        // Reset this._cacheExpirations to its initial state.\n        this._cacheExpirations = new Map();\n    }\n}\nexport { ExpirationPlugin };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:cacheable-response:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport './_version.js';\n/**\n * This class allows you to set up rules determining what\n * status codes and/or headers need to be present in order for a\n * [`Response`](https://developer.mozilla.org/en-US/docs/Web/API/Response)\n * to be considered cacheable.\n *\n * @memberof workbox-cacheable-response\n */\nclass CacheableResponse {\n    /**\n     * To construct a new CacheableResponse instance you must provide at least\n     * one of the `config` properties.\n     *\n     * If both `statuses` and `headers` are specified, then both conditions must\n     * be met for the `Response` to be considered cacheable.\n     *\n     * @param {Object} config\n     * @param {Array<number>} [config.statuses] One or more status codes that a\n     * `Response` can have and be considered cacheable.\n     * @param {Object<string,string>} [config.headers] A mapping of header names\n     * and expected values that a `Response` can have and be considered cacheable.\n     * If multiple headers are provided, only one needs to be present.\n     */\n    constructor(config = {}) {\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(config.statuses || config.headers)) {\n                throw new WorkboxError('statuses-or-headers-required', {\n                    moduleName: 'workbox-cacheable-response',\n                    className: 'CacheableResponse',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.statuses) {\n                assert.isArray(config.statuses, {\n                    moduleName: 'workbox-cacheable-response',\n                    className: 'CacheableResponse',\n                    funcName: 'constructor',\n                    paramName: 'config.statuses',\n                });\n            }\n            if (config.headers) {\n                assert.isType(config.headers, 'object', {\n                    moduleName: 'workbox-cacheable-response',\n                    className: 'CacheableResponse',\n                    funcName: 'constructor',\n                    paramName: 'config.headers',\n                });\n            }\n        }\n        this._statuses = config.statuses;\n        this._headers = config.headers;\n    }\n    /**\n     * Checks a response to see whether it's cacheable or not, based on this\n     * object's configuration.\n     *\n     * @param {Response} response The response whose cacheability is being\n     * checked.\n     * @return {boolean} `true` if the `Response` is cacheable, and `false`\n     * otherwise.\n     */\n    isResponseCacheable(response) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(response, Response, {\n                moduleName: 'workbox-cacheable-response',\n                className: 'CacheableResponse',\n                funcName: 'isResponseCacheable',\n                paramName: 'response',\n            });\n        }\n        let cacheable = true;\n        if (this._statuses) {\n            cacheable = this._statuses.includes(response.status);\n        }\n        if (this._headers && cacheable) {\n            cacheable = Object.keys(this._headers).some((headerName) => {\n                return response.headers.get(headerName) === this._headers[headerName];\n            });\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (!cacheable) {\n                logger.groupCollapsed(`The request for ` +\n                    `'${getFriendlyURL(response.url)}' returned a response that does ` +\n                    `not meet the criteria for being cached.`);\n                logger.groupCollapsed(`View cacheability criteria here.`);\n                logger.log(`Cacheable statuses: ` + JSON.stringify(this._statuses));\n                logger.log(`Cacheable headers: ` + JSON.stringify(this._headers, null, 2));\n                logger.groupEnd();\n                const logFriendlyHeaders = {};\n                response.headers.forEach((value, key) => {\n                    logFriendlyHeaders[key] = value;\n                });\n                logger.groupCollapsed(`View response status and headers here.`);\n                logger.log(`Response status: ${response.status}`);\n                logger.log(`Response headers: ` + JSON.stringify(logFriendlyHeaders, null, 2));\n                logger.groupEnd();\n                logger.groupCollapsed(`View full response details here.`);\n                logger.log(response.headers);\n                logger.log(response);\n                logger.groupEnd();\n                logger.groupEnd();\n            }\n        }\n        return cacheable;\n    }\n}\nexport { CacheableResponse };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { CacheableResponse, } from './CacheableResponse.js';\nimport './_version.js';\n/**\n * A class implementing the `cacheWillUpdate` lifecycle callback. This makes it\n * easier to add in cacheability checks to requests made via Workbox's built-in\n * strategies.\n *\n * @memberof workbox-cacheable-response\n */\nclass CacheableResponsePlugin {\n    /**\n     * To construct a new CacheableResponsePlugin instance you must provide at\n     * least one of the `config` properties.\n     *\n     * If both `statuses` and `headers` are specified, then both conditions must\n     * be met for the `Response` to be considered cacheable.\n     *\n     * @param {Object} config\n     * @param {Array<number>} [config.statuses] One or more status codes that a\n     * `Response` can have and be considered cacheable.\n     * @param {Object<string,string>} [config.headers] A mapping of header names\n     * and expected values that a `Response` can have and be considered cacheable.\n     * If multiple headers are provided, only one needs to be present.\n     */\n    constructor(config) {\n        /**\n         * @param {Object} options\n         * @param {Response} options.response\n         * @return {Response|null}\n         * @private\n         */\n        this.cacheWillUpdate = async ({ response }) => {\n            if (this._cacheableResponse.isResponseCacheable(response)) {\n                return response;\n            }\n            return null;\n        };\n        this._cacheableResponse = new CacheableResponse(config);\n    }\n}\nexport { CacheableResponsePlugin };\n", "/*\n  Copyright 2020 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nfunction stripParams(fullURL, ignoreParams) {\n    const strippedURL = new URL(fullURL);\n    for (const param of ignoreParams) {\n        strippedURL.searchParams.delete(param);\n    }\n    return strippedURL.href;\n}\n/**\n * Matches an item in the cache, ignoring specific URL params. This is similar\n * to the `ignoreSearch` option, but it allows you to ignore just specific\n * params (while continuing to match on the others).\n *\n * @private\n * @param {Cache} cache\n * @param {Request} request\n * @param {Object} matchOptions\n * @param {Array<string>} ignoreParams\n * @return {Promise<Response|undefined>}\n */\nasync function cacheMatchIgnoreParams(cache, request, ignoreParams, matchOptions) {\n    const strippedRequestURL = stripParams(request.url, ignoreParams);\n    // If the request doesn't include any ignored params, match as normal.\n    if (request.url === strippedRequestURL) {\n        return cache.match(request, matchOptions);\n    }\n    // Otherwise, match by comparing keys\n    const keysOptions = Object.assign(Object.assign({}, matchOptions), { ignoreSearch: true });\n    const cacheKeys = await cache.keys(request, keysOptions);\n    for (const cacheKey of cacheKeys) {\n        const strippedCacheKeyURL = stripParams(cacheKey.url, ignoreParams);\n        if (strippedRequestURL === strippedCacheKeyURL) {\n            return cache.match(cacheKey, matchOptions);\n        }\n    }\n    return;\n}\nexport { cacheMatchIgnoreParams };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The Deferred class composes Promises in a way that allows for them to be\n * resolved or rejected from outside the constructor. In most cases promises\n * should be used directly, but Deferreds can be necessary when the logic to\n * resolve a promise must be separate.\n *\n * @private\n */\nclass Deferred {\n    /**\n     * Creates a promise and exposes its resolve and reject functions as methods.\n     */\n    constructor() {\n        this.promise = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n    }\n}\nexport { Deferred };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from '../_private/logger.js';\nimport { quotaErrorCallbacks } from '../models/quotaErrorCallbacks.js';\nimport '../_version.js';\n/**\n * Runs all of the callback functions, one at a time sequentially, in the order\n * in which they were registered.\n *\n * @memberof workbox-core\n * @private\n */\nasync function executeQuotaErrorCallbacks() {\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log(`About to run ${quotaErrorCallbacks.size} ` +\n            `callbacks to clean up caches.`);\n    }\n    for (const callback of quotaErrorCallbacks) {\n        await callback();\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(callback, 'is complete.');\n        }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Finished running callbacks.');\n    }\n}\nexport { executeQuotaErrorCallbacks };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Returns a promise that resolves and the passed number of milliseconds.\n * This utility is an async/await-friendly version of `setTimeout`.\n *\n * @param {number} ms\n * @return {Promise}\n * @private\n */\nexport function timeout(ms) {\n    return new Promise((resolve) => setTimeout(resolve, ms));\n}\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:strategies:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheMatchIgnoreParams } from 'workbox-core/_private/cacheMatchIgnoreParams.js';\nimport { Deferred } from 'workbox-core/_private/Deferred.js';\nimport { executeQuotaErrorCallbacks } from 'workbox-core/_private/executeQuotaErrorCallbacks.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { timeout } from 'workbox-core/_private/timeout.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport './_version.js';\nfunction toRequest(input) {\n    return typeof input === 'string' ? new Request(input) : input;\n}\n/**\n * A class created every time a Strategy instance instance calls\n * {@link workbox-strategies.Strategy~handle} or\n * {@link workbox-strategies.Strategy~handleAll} that wraps all fetch and\n * cache actions around plugin callbacks and keeps track of when the strategy\n * is \"done\" (i.e. all added `event.waitUntil()` promises have resolved).\n *\n * @memberof workbox-strategies\n */\nclass StrategyHandler {\n    /**\n     * Creates a new instance associated with the passed strategy and event\n     * that's handling the request.\n     *\n     * The constructor also initializes the state that will be passed to each of\n     * the plugins handling this request.\n     *\n     * @param {workbox-strategies.Strategy} strategy\n     * @param {Object} options\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params] The return value from the\n     *     {@link workbox-routing~matchCallback} (if applicable).\n     */\n    constructor(strategy, options) {\n        this._cacheKeys = {};\n        /**\n         * The request the strategy is performing (passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * @name request\n         * @instance\n         * @type {Request}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        /**\n         * The event associated with this request.\n         * @name event\n         * @instance\n         * @type {ExtendableEvent}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        /**\n         * A `URL` instance of `request.url` (if passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * Note: the `url` param will be present if the strategy was invoked\n         * from a workbox `Route` object.\n         * @name url\n         * @instance\n         * @type {URL|undefined}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        /**\n         * A `param` value (if passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * Note: the `param` param will be present if the strategy was invoked\n         * from a workbox `Route` object and the\n         * {@link workbox-routing~matchCallback} returned\n         * a truthy value (it will be that value).\n         * @name params\n         * @instance\n         * @type {*|undefined}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(options.event, ExtendableEvent, {\n                moduleName: 'workbox-strategies',\n                className: 'StrategyHandler',\n                funcName: 'constructor',\n                paramName: 'options.event',\n            });\n        }\n        Object.assign(this, options);\n        this.event = options.event;\n        this._strategy = strategy;\n        this._handlerDeferred = new Deferred();\n        this._extendLifetimePromises = [];\n        // Copy the plugins list (since it's mutable on the strategy),\n        // so any mutations don't affect this handler instance.\n        this._plugins = [...strategy.plugins];\n        this._pluginStateMap = new Map();\n        for (const plugin of this._plugins) {\n            this._pluginStateMap.set(plugin, {});\n        }\n        this.event.waitUntil(this._handlerDeferred.promise);\n    }\n    /**\n     * Fetches a given request (and invokes any applicable plugin callback\n     * methods) using the `fetchOptions` (for non-navigation requests) and\n     * `plugins` defined on the `Strategy` object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - `requestWillFetch()`\n     * - `fetchDidSucceed()`\n     * - `fetchDidFail()`\n     *\n     * @param {Request|string} input The URL or request to fetch.\n     * @return {Promise<Response>}\n     */\n    async fetch(input) {\n        const { event } = this;\n        let request = toRequest(input);\n        if (request.mode === 'navigate' &&\n            event instanceof FetchEvent &&\n            event.preloadResponse) {\n            const possiblePreloadResponse = (await event.preloadResponse);\n            if (possiblePreloadResponse) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Using a preloaded navigation response for ` +\n                        `'${getFriendlyURL(request.url)}'`);\n                }\n                return possiblePreloadResponse;\n            }\n        }\n        // If there is a fetchDidFail plugin, we need to save a clone of the\n        // original request before it's either modified by a requestWillFetch\n        // plugin or before the original request's body is consumed via fetch().\n        const originalRequest = this.hasCallback('fetchDidFail')\n            ? request.clone()\n            : null;\n        try {\n            for (const cb of this.iterateCallbacks('requestWillFetch')) {\n                request = await cb({ request: request.clone(), event });\n            }\n        }\n        catch (err) {\n            if (err instanceof Error) {\n                throw new WorkboxError('plugin-error-request-will-fetch', {\n                    thrownErrorMessage: err.message,\n                });\n            }\n        }\n        // The request can be altered by plugins with `requestWillFetch` making\n        // the original request (most likely from a `fetch` event) different\n        // from the Request we make. Pass both to `fetchDidFail` to aid debugging.\n        const pluginFilteredRequest = request.clone();\n        try {\n            let fetchResponse;\n            // See https://github.com/GoogleChrome/workbox/issues/1796\n            fetchResponse = await fetch(request, request.mode === 'navigate' ? undefined : this._strategy.fetchOptions);\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Network request for ` +\n                    `'${getFriendlyURL(request.url)}' returned a response with ` +\n                    `status '${fetchResponse.status}'.`);\n            }\n            for (const callback of this.iterateCallbacks('fetchDidSucceed')) {\n                fetchResponse = await callback({\n                    event,\n                    request: pluginFilteredRequest,\n                    response: fetchResponse,\n                });\n            }\n            return fetchResponse;\n        }\n        catch (error) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log(`Network request for ` +\n                    `'${getFriendlyURL(request.url)}' threw an error.`, error);\n            }\n            // `originalRequest` will only exist if a `fetchDidFail` callback\n            // is being used (see above).\n            if (originalRequest) {\n                await this.runCallbacks('fetchDidFail', {\n                    error: error,\n                    event,\n                    originalRequest: originalRequest.clone(),\n                    request: pluginFilteredRequest.clone(),\n                });\n            }\n            throw error;\n        }\n    }\n    /**\n     * Calls `this.fetch()` and (in the background) runs `this.cachePut()` on\n     * the response generated by `this.fetch()`.\n     *\n     * The call to `this.cachePut()` automatically invokes `this.waitUntil()`,\n     * so you do not have to manually call `waitUntil()` on the event.\n     *\n     * @param {Request|string} input The request or URL to fetch and cache.\n     * @return {Promise<Response>}\n     */\n    async fetchAndCachePut(input) {\n        const response = await this.fetch(input);\n        const responseClone = response.clone();\n        void this.waitUntil(this.cachePut(input, responseClone));\n        return response;\n    }\n    /**\n     * Matches a request from the cache (and invokes any applicable plugin\n     * callback methods) using the `cacheName`, `matchOptions`, and `plugins`\n     * defined on the strategy object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - cacheKeyWillBeUsed()\n     * - cachedResponseWillBeUsed()\n     *\n     * @param {Request|string} key The Request or URL to use as the cache key.\n     * @return {Promise<Response|undefined>} A matching response, if found.\n     */\n    async cacheMatch(key) {\n        const request = toRequest(key);\n        let cachedResponse;\n        const { cacheName, matchOptions } = this._strategy;\n        const effectiveRequest = await this.getCacheKey(request, 'read');\n        const multiMatchOptions = Object.assign(Object.assign({}, matchOptions), { cacheName });\n        cachedResponse = await caches.match(effectiveRequest, multiMatchOptions);\n        if (process.env.NODE_ENV !== 'production') {\n            if (cachedResponse) {\n                logger.debug(`Found a cached response in '${cacheName}'.`);\n            }\n            else {\n                logger.debug(`No cached response found in '${cacheName}'.`);\n            }\n        }\n        for (const callback of this.iterateCallbacks('cachedResponseWillBeUsed')) {\n            cachedResponse =\n                (await callback({\n                    cacheName,\n                    matchOptions,\n                    cachedResponse,\n                    request: effectiveRequest,\n                    event: this.event,\n                })) || undefined;\n        }\n        return cachedResponse;\n    }\n    /**\n     * Puts a request/response pair in the cache (and invokes any applicable\n     * plugin callback methods) using the `cacheName` and `plugins` defined on\n     * the strategy object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - cacheKeyWillBeUsed()\n     * - cacheWillUpdate()\n     * - cacheDidUpdate()\n     *\n     * @param {Request|string} key The request or URL to use as the cache key.\n     * @param {Response} response The response to cache.\n     * @return {Promise<boolean>} `false` if a cacheWillUpdate caused the response\n     * not be cached, and `true` otherwise.\n     */\n    async cachePut(key, response) {\n        const request = toRequest(key);\n        // Run in the next task to avoid blocking other cache reads.\n        // https://github.com/w3c/ServiceWorker/issues/1397\n        await timeout(0);\n        const effectiveRequest = await this.getCacheKey(request, 'write');\n        if (process.env.NODE_ENV !== 'production') {\n            if (effectiveRequest.method && effectiveRequest.method !== 'GET') {\n                throw new WorkboxError('attempt-to-cache-non-get-request', {\n                    url: getFriendlyURL(effectiveRequest.url),\n                    method: effectiveRequest.method,\n                });\n            }\n            // See https://github.com/GoogleChrome/workbox/issues/2818\n            const vary = response.headers.get('Vary');\n            if (vary) {\n                logger.debug(`The response for ${getFriendlyURL(effectiveRequest.url)} ` +\n                    `has a 'Vary: ${vary}' header. ` +\n                    `Consider setting the {ignoreVary: true} option on your strategy ` +\n                    `to ensure cache matching and deletion works as expected.`);\n            }\n        }\n        if (!response) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.error(`Cannot cache non-existent response for ` +\n                    `'${getFriendlyURL(effectiveRequest.url)}'.`);\n            }\n            throw new WorkboxError('cache-put-with-no-response', {\n                url: getFriendlyURL(effectiveRequest.url),\n            });\n        }\n        const responseToCache = await this._ensureResponseSafeToCache(response);\n        if (!responseToCache) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Response '${getFriendlyURL(effectiveRequest.url)}' ` +\n                    `will not be cached.`, responseToCache);\n            }\n            return false;\n        }\n        const { cacheName, matchOptions } = this._strategy;\n        const cache = await self.caches.open(cacheName);\n        const hasCacheUpdateCallback = this.hasCallback('cacheDidUpdate');\n        const oldResponse = hasCacheUpdateCallback\n            ? await cacheMatchIgnoreParams(\n            // TODO(philipwalton): the `__WB_REVISION__` param is a precaching\n            // feature. Consider into ways to only add this behavior if using\n            // precaching.\n            cache, effectiveRequest.clone(), ['__WB_REVISION__'], matchOptions)\n            : null;\n        if (process.env.NODE_ENV !== 'production') {\n            logger.debug(`Updating the '${cacheName}' cache with a new Response ` +\n                `for ${getFriendlyURL(effectiveRequest.url)}.`);\n        }\n        try {\n            await cache.put(effectiveRequest, hasCacheUpdateCallback ? responseToCache.clone() : responseToCache);\n        }\n        catch (error) {\n            if (error instanceof Error) {\n                // See https://developer.mozilla.org/en-US/docs/Web/API/DOMException#exception-QuotaExceededError\n                if (error.name === 'QuotaExceededError') {\n                    await executeQuotaErrorCallbacks();\n                }\n                throw error;\n            }\n        }\n        for (const callback of this.iterateCallbacks('cacheDidUpdate')) {\n            await callback({\n                cacheName,\n                oldResponse,\n                newResponse: responseToCache.clone(),\n                request: effectiveRequest,\n                event: this.event,\n            });\n        }\n        return true;\n    }\n    /**\n     * Checks the list of plugins for the `cacheKeyWillBeUsed` callback, and\n     * executes any of those callbacks found in sequence. The final `Request`\n     * object returned by the last plugin is treated as the cache key for cache\n     * reads and/or writes. If no `cacheKeyWillBeUsed` plugin callbacks have\n     * been registered, the passed request is returned unmodified\n     *\n     * @param {Request} request\n     * @param {string} mode\n     * @return {Promise<Request>}\n     */\n    async getCacheKey(request, mode) {\n        const key = `${request.url} | ${mode}`;\n        if (!this._cacheKeys[key]) {\n            let effectiveRequest = request;\n            for (const callback of this.iterateCallbacks('cacheKeyWillBeUsed')) {\n                effectiveRequest = toRequest(await callback({\n                    mode,\n                    request: effectiveRequest,\n                    event: this.event,\n                    // params has a type any can't change right now.\n                    params: this.params, // eslint-disable-line\n                }));\n            }\n            this._cacheKeys[key] = effectiveRequest;\n        }\n        return this._cacheKeys[key];\n    }\n    /**\n     * Returns true if the strategy has at least one plugin with the given\n     * callback.\n     *\n     * @param {string} name The name of the callback to check for.\n     * @return {boolean}\n     */\n    hasCallback(name) {\n        for (const plugin of this._strategy.plugins) {\n            if (name in plugin) {\n                return true;\n            }\n        }\n        return false;\n    }\n    /**\n     * Runs all plugin callbacks matching the given name, in order, passing the\n     * given param object (merged ith the current plugin state) as the only\n     * argument.\n     *\n     * Note: since this method runs all plugins, it's not suitable for cases\n     * where the return value of a callback needs to be applied prior to calling\n     * the next callback. See\n     * {@link workbox-strategies.StrategyHandler#iterateCallbacks}\n     * below for how to handle that case.\n     *\n     * @param {string} name The name of the callback to run within each plugin.\n     * @param {Object} param The object to pass as the first (and only) param\n     *     when executing each callback. This object will be merged with the\n     *     current plugin state prior to callback execution.\n     */\n    async runCallbacks(name, param) {\n        for (const callback of this.iterateCallbacks(name)) {\n            // TODO(philipwalton): not sure why `any` is needed. It seems like\n            // this should work with `as WorkboxPluginCallbackParam[C]`.\n            await callback(param);\n        }\n    }\n    /**\n     * Accepts a callback and returns an iterable of matching plugin callbacks,\n     * where each callback is wrapped with the current handler state (i.e. when\n     * you call each callback, whatever object parameter you pass it will\n     * be merged with the plugin's current state).\n     *\n     * @param {string} name The name fo the callback to run\n     * @return {Array<Function>}\n     */\n    *iterateCallbacks(name) {\n        for (const plugin of this._strategy.plugins) {\n            if (typeof plugin[name] === 'function') {\n                const state = this._pluginStateMap.get(plugin);\n                const statefulCallback = (param) => {\n                    const statefulParam = Object.assign(Object.assign({}, param), { state });\n                    // TODO(philipwalton): not sure why `any` is needed. It seems like\n                    // this should work with `as WorkboxPluginCallbackParam[C]`.\n                    return plugin[name](statefulParam);\n                };\n                yield statefulCallback;\n            }\n        }\n    }\n    /**\n     * Adds a promise to the\n     * [extend lifetime promises]{@link https://w3c.github.io/ServiceWorker/#extendableevent-extend-lifetime-promises}\n     * of the event event associated with the request being handled (usually a\n     * `FetchEvent`).\n     *\n     * Note: you can await\n     * {@link workbox-strategies.StrategyHandler~doneWaiting}\n     * to know when all added promises have settled.\n     *\n     * @param {Promise} promise A promise to add to the extend lifetime promises\n     *     of the event that triggered the request.\n     */\n    waitUntil(promise) {\n        this._extendLifetimePromises.push(promise);\n        return promise;\n    }\n    /**\n     * Returns a promise that resolves once all promises passed to\n     * {@link workbox-strategies.StrategyHandler~waitUntil}\n     * have settled.\n     *\n     * Note: any work done after `doneWaiting()` settles should be manually\n     * passed to an event's `waitUntil()` method (not this handler's\n     * `waitUntil()` method), otherwise the service worker thread my be killed\n     * prior to your work completing.\n     */\n    async doneWaiting() {\n        let promise;\n        while ((promise = this._extendLifetimePromises.shift())) {\n            await promise;\n        }\n    }\n    /**\n     * Stops running the strategy and immediately resolves any pending\n     * `waitUntil()` promises.\n     */\n    destroy() {\n        this._handlerDeferred.resolve(null);\n    }\n    /**\n     * This method will call cacheWillUpdate on the available plugins (or use\n     * status === 200) to determine if the Response is safe and valid to cache.\n     *\n     * @param {Request} options.request\n     * @param {Response} options.response\n     * @return {Promise<Response|undefined>}\n     *\n     * @private\n     */\n    async _ensureResponseSafeToCache(response) {\n        let responseToCache = response;\n        let pluginsUsed = false;\n        for (const callback of this.iterateCallbacks('cacheWillUpdate')) {\n            responseToCache =\n                (await callback({\n                    request: this.request,\n                    response: responseToCache,\n                    event: this.event,\n                })) || undefined;\n            pluginsUsed = true;\n            if (!responseToCache) {\n                break;\n            }\n        }\n        if (!pluginsUsed) {\n            if (responseToCache && responseToCache.status !== 200) {\n                responseToCache = undefined;\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                if (responseToCache) {\n                    if (responseToCache.status !== 200) {\n                        if (responseToCache.status === 0) {\n                            logger.warn(`The response for '${this.request.url}' ` +\n                                `is an opaque response. The caching strategy that you're ` +\n                                `using will not cache opaque responses by default.`);\n                        }\n                        else {\n                            logger.debug(`The response for '${this.request.url}' ` +\n                                `returned a status code of '${response.status}' and won't ` +\n                                `be cached as a result.`);\n                        }\n                    }\n                }\n            }\n        }\n        return responseToCache;\n    }\n}\nexport { StrategyHandler };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { StrategyHandler } from './StrategyHandler.js';\nimport './_version.js';\n/**\n * An abstract base class that all other strategy classes must extend from:\n *\n * @memberof workbox-strategies\n */\nclass Strategy {\n    /**\n     * Creates a new instance of the strategy and sets all documented option\n     * properties as public instance properties.\n     *\n     * Note: if a custom strategy class extends the base Strategy class and does\n     * not need more than these properties, it does not need to define its own\n     * constructor.\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to the cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {Object} [options.matchOptions] The\n     * [`CacheQueryOptions`]{@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions}\n     * for any `cache.match()` or `cache.put()` calls made by this strategy.\n     */\n    constructor(options = {}) {\n        /**\n         * Cache name to store and retrieve\n         * requests. Defaults to the cache names provided by\n         * {@link workbox-core.cacheNames}.\n         *\n         * @type {string}\n         */\n        this.cacheName = cacheNames.getRuntimeName(options.cacheName);\n        /**\n         * The list\n         * [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n         * used by this strategy.\n         *\n         * @type {Array<Object>}\n         */\n        this.plugins = options.plugins || [];\n        /**\n         * Values passed along to the\n         * [`init`]{@link https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters}\n         * of all fetch() requests made by this strategy.\n         *\n         * @type {Object}\n         */\n        this.fetchOptions = options.fetchOptions;\n        /**\n         * The\n         * [`CacheQueryOptions`]{@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions}\n         * for any `cache.match()` or `cache.put()` calls made by this strategy.\n         *\n         * @type {Object}\n         */\n        this.matchOptions = options.matchOptions;\n    }\n    /**\n     * Perform a request strategy and returns a `Promise` that will resolve with\n     * a `Response`, invoking all relevant plugin callbacks.\n     *\n     * When a strategy instance is registered with a Workbox\n     * {@link workbox-routing.Route}, this method is automatically\n     * called when the route matches.\n     *\n     * Alternatively, this method can be used in a standalone `FetchEvent`\n     * listener by passing it to `event.respondWith()`.\n     *\n     * @param {FetchEvent|Object} options A `FetchEvent` or an object with the\n     *     properties listed below.\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params]\n     */\n    handle(options) {\n        const [responseDone] = this.handleAll(options);\n        return responseDone;\n    }\n    /**\n     * Similar to {@link workbox-strategies.Strategy~handle}, but\n     * instead of just returning a `Promise` that resolves to a `Response` it\n     * it will return an tuple of `[response, done]` promises, where the former\n     * (`response`) is equivalent to what `handle()` returns, and the latter is a\n     * Promise that will resolve once any promises that were added to\n     * `event.waitUntil()` as part of performing the strategy have completed.\n     *\n     * You can await the `done` promise to ensure any extra work performed by\n     * the strategy (usually caching responses) completes successfully.\n     *\n     * @param {FetchEvent|Object} options A `FetchEvent` or an object with the\n     *     properties listed below.\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params]\n     * @return {Array<Promise>} A tuple of [response, done]\n     *     promises that can be used to determine when the response resolves as\n     *     well as when the handler has completed all its work.\n     */\n    handleAll(options) {\n        // Allow for flexible options to be passed.\n        if (options instanceof FetchEvent) {\n            options = {\n                event: options,\n                request: options.request,\n            };\n        }\n        const event = options.event;\n        const request = typeof options.request === 'string'\n            ? new Request(options.request)\n            : options.request;\n        const params = 'params' in options ? options.params : undefined;\n        const handler = new StrategyHandler(this, { event, request, params });\n        const responseDone = this._getResponse(handler, request, event);\n        const handlerDone = this._awaitComplete(responseDone, handler, request, event);\n        // Return an array of promises, suitable for use with Promise.all().\n        return [responseDone, handlerDone];\n    }\n    async _getResponse(handler, request, event) {\n        await handler.runCallbacks('handlerWillStart', { event, request });\n        let response = undefined;\n        try {\n            response = await this._handle(request, handler);\n            // The \"official\" Strategy subclasses all throw this error automatically,\n            // but in case a third-party Strategy doesn't, ensure that we have a\n            // consistent failure when there's no response or an error response.\n            if (!response || response.type === 'error') {\n                throw new WorkboxError('no-response', { url: request.url });\n            }\n        }\n        catch (error) {\n            if (error instanceof Error) {\n                for (const callback of handler.iterateCallbacks('handlerDidError')) {\n                    response = await callback({ error, event, request });\n                    if (response) {\n                        break;\n                    }\n                }\n            }\n            if (!response) {\n                throw error;\n            }\n            else if (process.env.NODE_ENV !== 'production') {\n                logger.log(`While responding to '${getFriendlyURL(request.url)}', ` +\n                    `an ${error instanceof Error ? error.toString() : ''} error occurred. Using a fallback response provided by ` +\n                    `a handlerDidError plugin.`);\n            }\n        }\n        for (const callback of handler.iterateCallbacks('handlerWillRespond')) {\n            response = await callback({ event, request, response });\n        }\n        return response;\n    }\n    async _awaitComplete(responseDone, handler, request, event) {\n        let response;\n        let error;\n        try {\n            response = await responseDone;\n        }\n        catch (error) {\n            // Ignore errors, as response errors should be caught via the `response`\n            // promise above. The `done` promise will only throw for errors in\n            // promises passed to `handler.waitUntil()`.\n        }\n        try {\n            await handler.runCallbacks('handlerDidRespond', {\n                event,\n                request,\n                response,\n            });\n            await handler.doneWaiting();\n        }\n        catch (waitUntilError) {\n            if (waitUntilError instanceof Error) {\n                error = waitUntilError;\n            }\n        }\n        await handler.runCallbacks('handlerDidComplete', {\n            event,\n            request,\n            response,\n            error: error,\n        });\n        handler.destroy();\n        if (error) {\n            throw error;\n        }\n    }\n}\nexport { Strategy };\n/**\n * Classes extending the `Strategy` based class should implement this method,\n * and leverage the {@link workbox-strategies.StrategyHandler}\n * arg to perform all fetching and cache logic, which will ensure all relevant\n * cache, cache options, fetch options and plugins are used (per the current\n * strategy instance).\n *\n * @name _handle\n * @instance\n * @abstract\n * @function\n * @param {Request} request\n * @param {workbox-strategies.StrategyHandler} handler\n * @return {Promise<Response>}\n *\n * @memberof workbox-strategies.Strategy\n */\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport '../_version.js';\nexport const messages = {\n    strategyStart: (strategyName, request) => `Using ${strategyName} to respond to '${getFriendlyURL(request.url)}'`,\n    printFinalResponse: (response) => {\n        if (response) {\n            logger.groupCollapsed(`View the final response here.`);\n            logger.log(response || '[No response returned]');\n            logger.groupEnd();\n        }\n    },\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a [cache-first](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#cache-first-falling-back-to-network)\n * request strategy.\n *\n * A cache first strategy is useful for assets that have been revisioned,\n * such as URLs like `/styles/example.a8f5f1.css`, since they\n * can be cached for long periods of time.\n *\n * If the network request fails, and there is no cache match, this will throw\n * a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass CacheFirst extends Strategy {\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const logs = [];\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'makeRequest',\n                paramName: 'request',\n            });\n        }\n        let response = await handler.cacheMatch(request);\n        let error = undefined;\n        if (!response) {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`No response found in the '${this.cacheName}' cache. ` +\n                    `Will respond with a network request.`);\n            }\n            try {\n                response = await handler.fetchAndCachePut(request);\n            }\n            catch (err) {\n                if (err instanceof Error) {\n                    error = err;\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                if (response) {\n                    logs.push(`Got response from network.`);\n                }\n                else {\n                    logs.push(`Unable to get a response from the network.`);\n                }\n            }\n        }\n        else {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`Found a cached response in the '${this.cacheName}' cache.`);\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            for (const log of logs) {\n                logger.log(log);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url, error });\n        }\n        return response;\n    }\n}\nexport { CacheFirst };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nexport const cacheOkAndOpaquePlugin = {\n    /**\n     * Returns a valid response (to allow caching) if the status is 200 (OK) or\n     * 0 (opaque).\n     *\n     * @param {Object} options\n     * @param {Response} options.response\n     * @return {Response|null}\n     *\n     * @private\n     */\n    cacheWillUpdate: async ({ response }) => {\n        if (response.status === 200 || response.status === 0) {\n            return response;\n        }\n        return null;\n    },\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { cacheOkAndOpaquePlugin } from './plugins/cacheOkAndOpaquePlugin.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a\n * [network first](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#network-first-falling-back-to-cache)\n * request strategy.\n *\n * By default, this strategy will cache responses with a 200 status code as\n * well as [opaque responses](https://developer.chrome.com/docs/workbox/caching-resources-during-runtime/#opaque-responses).\n * Opaque responses are are cross-origin requests where the response doesn't\n * support [CORS](https://enable-cors.org/).\n *\n * If the network request fails, and there is no cache match, this will throw\n * a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass NetworkFirst extends Strategy {\n    /**\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {Object} [options.matchOptions] [`CacheQueryOptions`](https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions)\n     * @param {number} [options.networkTimeoutSeconds] If set, any network requests\n     * that fail to respond within the timeout will fallback to the cache.\n     *\n     * This option can be used to combat\n     * \"[lie-fi]{@link https://developers.google.com/web/fundamentals/performance/poor-connectivity/#lie-fi}\"\n     * scenarios.\n     */\n    constructor(options = {}) {\n        super(options);\n        // If this instance contains no plugins with a 'cacheWillUpdate' callback,\n        // prepend the `cacheOkAndOpaquePlugin` plugin to the plugins list.\n        if (!this.plugins.some((p) => 'cacheWillUpdate' in p)) {\n            this.plugins.unshift(cacheOkAndOpaquePlugin);\n        }\n        this._networkTimeoutSeconds = options.networkTimeoutSeconds || 0;\n        if (process.env.NODE_ENV !== 'production') {\n            if (this._networkTimeoutSeconds) {\n                assert.isType(this._networkTimeoutSeconds, 'number', {\n                    moduleName: 'workbox-strategies',\n                    className: this.constructor.name,\n                    funcName: 'constructor',\n                    paramName: 'networkTimeoutSeconds',\n                });\n            }\n        }\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const logs = [];\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'handle',\n                paramName: 'makeRequest',\n            });\n        }\n        const promises = [];\n        let timeoutId;\n        if (this._networkTimeoutSeconds) {\n            const { id, promise } = this._getTimeoutPromise({ request, logs, handler });\n            timeoutId = id;\n            promises.push(promise);\n        }\n        const networkPromise = this._getNetworkPromise({\n            timeoutId,\n            request,\n            logs,\n            handler,\n        });\n        promises.push(networkPromise);\n        const response = await handler.waitUntil((async () => {\n            // Promise.race() will resolve as soon as the first promise resolves.\n            return ((await handler.waitUntil(Promise.race(promises))) ||\n                // If Promise.race() resolved with null, it might be due to a network\n                // timeout + a cache miss. If that were to happen, we'd rather wait until\n                // the networkPromise resolves instead of returning null.\n                // Note that it's fine to await an already-resolved promise, so we don't\n                // have to check to see if it's still \"in flight\".\n                (await networkPromise));\n        })());\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            for (const log of logs) {\n                logger.log(log);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url });\n        }\n        return response;\n    }\n    /**\n     * @param {Object} options\n     * @param {Request} options.request\n     * @param {Array} options.logs A reference to the logs array\n     * @param {Event} options.event\n     * @return {Promise<Response>}\n     *\n     * @private\n     */\n    _getTimeoutPromise({ request, logs, handler, }) {\n        let timeoutId;\n        const timeoutPromise = new Promise((resolve) => {\n            const onNetworkTimeout = async () => {\n                if (process.env.NODE_ENV !== 'production') {\n                    logs.push(`Timing out the network response at ` +\n                        `${this._networkTimeoutSeconds} seconds.`);\n                }\n                resolve(await handler.cacheMatch(request));\n            };\n            timeoutId = setTimeout(onNetworkTimeout, this._networkTimeoutSeconds * 1000);\n        });\n        return {\n            promise: timeoutPromise,\n            id: timeoutId,\n        };\n    }\n    /**\n     * @param {Object} options\n     * @param {number|undefined} options.timeoutId\n     * @param {Request} options.request\n     * @param {Array} options.logs A reference to the logs Array.\n     * @param {Event} options.event\n     * @return {Promise<Response>}\n     *\n     * @private\n     */\n    async _getNetworkPromise({ timeoutId, request, logs, handler, }) {\n        let error;\n        let response;\n        try {\n            response = await handler.fetchAndCachePut(request);\n        }\n        catch (fetchError) {\n            if (fetchError instanceof Error) {\n                error = fetchError;\n            }\n        }\n        if (timeoutId) {\n            clearTimeout(timeoutId);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (response) {\n                logs.push(`Got response from network.`);\n            }\n            else {\n                logs.push(`Unable to get a response from the network. Will respond ` +\n                    `with a cached response.`);\n            }\n        }\n        if (error || !response) {\n            response = await handler.cacheMatch(request);\n            if (process.env.NODE_ENV !== 'production') {\n                if (response) {\n                    logs.push(`Found a cached response in the '${this.cacheName}'` + ` cache.`);\n                }\n                else {\n                    logs.push(`No response found in the '${this.cacheName}' cache.`);\n                }\n            }\n        }\n        return response;\n    }\n}\nexport { NetworkFirst };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport './_version.js';\n/**\n * Claim any currently available clients once the service worker\n * becomes active. This is normally used in conjunction with `skipWaiting()`.\n *\n * @memberof workbox-core\n */\nfunction clientsClaim() {\n    self.addEventListener('activate', () => self.clients.claim());\n}\nexport { clientsClaim };\n", "/*\n  Copyright 2020 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A utility method that makes it easier to use `event.waitUntil` with\n * async functions and return the result.\n *\n * @param {ExtendableEvent} event\n * @param {Function} asyncFn\n * @return {Function}\n * @private\n */\nfunction waitUntil(event, asyncFn) {\n    const returnPromise = asyncFn();\n    event.waitUntil(returnPromise);\n    return returnPromise;\n}\nexport { waitUntil };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:precaching:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport '../_version.js';\n// Name of the search parameter used to store revision info.\nconst REVISION_SEARCH_PARAM = '__WB_REVISION__';\n/**\n * Converts a manifest entry into a versioned URL suitable for precaching.\n *\n * @param {Object|string} entry\n * @return {string} A URL with versioning info.\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function createCacheKey(entry) {\n    if (!entry) {\n        throw new WorkboxError('add-to-cache-list-unexpected-type', { entry });\n    }\n    // If a precache manifest entry is a string, it's assumed to be a versioned\n    // URL, like '/app.abcd1234.js'. Return as-is.\n    if (typeof entry === 'string') {\n        const urlObject = new URL(entry, location.href);\n        return {\n            cacheKey: urlObject.href,\n            url: urlObject.href,\n        };\n    }\n    const { revision, url } = entry;\n    if (!url) {\n        throw new WorkboxError('add-to-cache-list-unexpected-type', { entry });\n    }\n    // If there's just a URL and no revision, then it's also assumed to be a\n    // versioned URL.\n    if (!revision) {\n        const urlObject = new URL(url, location.href);\n        return {\n            cacheKey: urlObject.href,\n            url: urlObject.href,\n        };\n    }\n    // Otherwise, construct a properly versioned URL using the custom Workbox\n    // search parameter along with the revision info.\n    const cacheKeyURL = new URL(url, location.href);\n    const originalURL = new URL(url, location.href);\n    cacheKeyURL.searchParams.set(REVISION_SEARCH_PARAM, revision);\n    return {\n        cacheKey: cacheKeyURL.href,\n        url: originalURL.href,\n    };\n}\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A plugin, designed to be used with PrecacheController, to determine the\n * of assets that were updated (or not updated) during the install event.\n *\n * @private\n */\nclass PrecacheInstallReportPlugin {\n    constructor() {\n        this.updatedURLs = [];\n        this.notUpdatedURLs = [];\n        this.handlerWillStart = async ({ request, state, }) => {\n            // TODO: `state` should never be undefined...\n            if (state) {\n                state.originalRequest = request;\n            }\n        };\n        this.cachedResponseWillBeUsed = async ({ event, state, cachedResponse, }) => {\n            if (event.type === 'install') {\n                if (state &&\n                    state.originalRequest &&\n                    state.originalRequest instanceof Request) {\n                    // TODO: `state` should never be undefined...\n                    const url = state.originalRequest.url;\n                    if (cachedResponse) {\n                        this.notUpdatedURLs.push(url);\n                    }\n                    else {\n                        this.updatedURLs.push(url);\n                    }\n                }\n            }\n            return cachedResponse;\n        };\n    }\n}\nexport { PrecacheInstallReportPlugin };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A plugin, designed to be used with PrecacheController, to translate URLs into\n * the corresponding cache key, based on the current revision info.\n *\n * @private\n */\nclass PrecacheCacheKeyPlugin {\n    constructor({ precacheController }) {\n        this.cacheKeyWillBeUsed = async ({ request, params, }) => {\n            // Params is type any, can't change right now.\n            /* eslint-disable */\n            const cacheKey = (params === null || params === void 0 ? void 0 : params.cacheKey) ||\n                this._precacheController.getCacheKeyForURL(request.url);\n            /* eslint-enable */\n            return cacheKey\n                ? new Request(cacheKey, { headers: request.headers })\n                : request;\n        };\n        this._precacheController = precacheController;\n    }\n}\nexport { PrecacheCacheKeyPlugin };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport '../_version.js';\n/**\n * @param {string} groupTitle\n * @param {Array<string>} deletedURLs\n *\n * @private\n */\nconst logGroup = (groupTitle, deletedURLs) => {\n    logger.groupCollapsed(groupTitle);\n    for (const url of deletedURLs) {\n        logger.log(url);\n    }\n    logger.groupEnd();\n};\n/**\n * @param {Array<string>} deletedURLs\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function printCleanupDetails(deletedURLs) {\n    const deletionCount = deletedURLs.length;\n    if (deletionCount > 0) {\n        logger.groupCollapsed(`During precaching cleanup, ` +\n            `${deletionCount} cached ` +\n            `request${deletionCount === 1 ? ' was' : 's were'} deleted.`);\n        logGroup('Deleted Cache Requests', deletedURLs);\n        logger.groupEnd();\n    }\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport '../_version.js';\n/**\n * @param {string} groupTitle\n * @param {Array<string>} urls\n *\n * @private\n */\nfunction _nestedGroup(groupTitle, urls) {\n    if (urls.length === 0) {\n        return;\n    }\n    logger.groupCollapsed(groupTitle);\n    for (const url of urls) {\n        logger.log(url);\n    }\n    logger.groupEnd();\n}\n/**\n * @param {Array<string>} urlsToPrecache\n * @param {Array<string>} urlsAlreadyPrecached\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function printInstallDetails(urlsToPrecache, urlsAlreadyPrecached) {\n    const precachedCount = urlsToPrecache.length;\n    const alreadyPrecachedCount = urlsAlreadyPrecached.length;\n    if (precachedCount || alreadyPrecachedCount) {\n        let message = `Precaching ${precachedCount} file${precachedCount === 1 ? '' : 's'}.`;\n        if (alreadyPrecachedCount > 0) {\n            message +=\n                ` ${alreadyPrecachedCount} ` +\n                    `file${alreadyPrecachedCount === 1 ? ' is' : 's are'} already cached.`;\n        }\n        logger.groupCollapsed(message);\n        _nestedGroup(`View newly precached URLs.`, urlsToPrecache);\n        _nestedGroup(`View previously precached URLs.`, urlsAlreadyPrecached);\n        logger.groupEnd();\n    }\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nlet supportStatus;\n/**\n * A utility function that determines whether the current browser supports\n * constructing a new `Response` from a `response.body` stream.\n *\n * @return {boolean} `true`, if the current browser can successfully\n *     construct a `Response` from a `response.body` stream, `false` otherwise.\n *\n * @private\n */\nfunction canConstructResponseFromBodyStream() {\n    if (supportStatus === undefined) {\n        const testResponse = new Response('');\n        if ('body' in testResponse) {\n            try {\n                new Response(testResponse.body);\n                supportStatus = true;\n            }\n            catch (error) {\n                supportStatus = false;\n            }\n        }\n        supportStatus = false;\n    }\n    return supportStatus;\n}\nexport { canConstructResponseFromBodyStream };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { canConstructResponseFromBodyStream } from './_private/canConstructResponseFromBodyStream.js';\nimport { WorkboxError } from './_private/WorkboxError.js';\nimport './_version.js';\n/**\n * Allows developers to copy a response and modify its `headers`, `status`,\n * or `statusText` values (the values settable via a\n * [`ResponseInit`]{@link https://developer.mozilla.org/en-US/docs/Web/API/Response/Response#Syntax}\n * object in the constructor).\n * To modify these values, pass a function as the second argument. That\n * function will be invoked with a single object with the response properties\n * `{headers, status, statusText}`. The return value of this function will\n * be used as the `ResponseInit` for the new `Response`. To change the values\n * either modify the passed parameter(s) and return it, or return a totally\n * new object.\n *\n * This method is intentionally limited to same-origin responses, regardless of\n * whether CORS was used or not.\n *\n * @param {Response} response\n * @param {Function} modifier\n * @memberof workbox-core\n */\nasync function copyResponse(response, modifier) {\n    let origin = null;\n    // If response.url isn't set, assume it's cross-origin and keep origin null.\n    if (response.url) {\n        const responseURL = new URL(response.url);\n        origin = responseURL.origin;\n    }\n    if (origin !== self.location.origin) {\n        throw new WorkboxError('cross-origin-copy-response', { origin });\n    }\n    const clonedResponse = response.clone();\n    // Create a fresh `ResponseInit` object by cloning the headers.\n    const responseInit = {\n        headers: new Headers(clonedResponse.headers),\n        status: clonedResponse.status,\n        statusText: clonedResponse.statusText,\n    };\n    // Apply any user modifications.\n    const modifiedResponseInit = modifier ? modifier(responseInit) : responseInit;\n    // Create the new response from the body stream and `ResponseInit`\n    // modifications. Note: not all browsers support the Response.body stream,\n    // so fall back to reading the entire body into memory as a blob.\n    const body = canConstructResponseFromBodyStream()\n        ? clonedResponse.body\n        : await clonedResponse.blob();\n    return new Response(body, modifiedResponseInit);\n}\nexport { copyResponse };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { copyResponse } from 'workbox-core/copyResponse.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from 'workbox-strategies/Strategy.js';\nimport './_version.js';\n/**\n * A {@link workbox-strategies.Strategy} implementation\n * specifically designed to work with\n * {@link workbox-precaching.PrecacheController}\n * to both cache and fetch precached assets.\n *\n * Note: an instance of this class is created automatically when creating a\n * `PrecacheController`; it's generally not necessary to create this yourself.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-precaching\n */\nclass PrecacheStrategy extends Strategy {\n    /**\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to the cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] {@link https://developers.google.com/web/tools/workbox/guides/using-plugins|Plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * {@link https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters|init}\n     * of all fetch() requests made by this strategy.\n     * @param {Object} [options.matchOptions] The\n     * {@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions|CacheQueryOptions}\n     * for any `cache.match()` or `cache.put()` calls made by this strategy.\n     * @param {boolean} [options.fallbackToNetwork=true] Whether to attempt to\n     * get the response from the network if there's a precache miss.\n     */\n    constructor(options = {}) {\n        options.cacheName = cacheNames.getPrecacheName(options.cacheName);\n        super(options);\n        this._fallbackToNetwork =\n            options.fallbackToNetwork === false ? false : true;\n        // Redirected responses cannot be used to satisfy a navigation request, so\n        // any redirected response must be \"copied\" rather than cloned, so the new\n        // response doesn't contain the `redirected` flag. See:\n        // https://bugs.chromium.org/p/chromium/issues/detail?id=669363&desc=2#c1\n        this.plugins.push(PrecacheStrategy.copyRedirectedCacheableResponsesPlugin);\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const response = await handler.cacheMatch(request);\n        if (response) {\n            return response;\n        }\n        // If this is an `install` event for an entry that isn't already cached,\n        // then populate the cache.\n        if (handler.event && handler.event.type === 'install') {\n            return await this._handleInstall(request, handler);\n        }\n        // Getting here means something went wrong. An entry that should have been\n        // precached wasn't found in the cache.\n        return await this._handleFetch(request, handler);\n    }\n    async _handleFetch(request, handler) {\n        let response;\n        const params = (handler.params || {});\n        // Fall back to the network if we're configured to do so.\n        if (this._fallbackToNetwork) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.warn(`The precached response for ` +\n                    `${getFriendlyURL(request.url)} in ${this.cacheName} was not ` +\n                    `found. Falling back to the network.`);\n            }\n            const integrityInManifest = params.integrity;\n            const integrityInRequest = request.integrity;\n            const noIntegrityConflict = !integrityInRequest || integrityInRequest === integrityInManifest;\n            // Do not add integrity if the original request is no-cors\n            // See https://github.com/GoogleChrome/workbox/issues/3096\n            response = await handler.fetch(new Request(request, {\n                integrity: request.mode !== 'no-cors'\n                    ? integrityInRequest || integrityInManifest\n                    : undefined,\n            }));\n            // It's only \"safe\" to repair the cache if we're using SRI to guarantee\n            // that the response matches the precache manifest's expectations,\n            // and there's either a) no integrity property in the incoming request\n            // or b) there is an integrity, and it matches the precache manifest.\n            // See https://github.com/GoogleChrome/workbox/issues/2858\n            // Also if the original request users no-cors we don't use integrity.\n            // See https://github.com/GoogleChrome/workbox/issues/3096\n            if (integrityInManifest &&\n                noIntegrityConflict &&\n                request.mode !== 'no-cors') {\n                this._useDefaultCacheabilityPluginIfNeeded();\n                const wasCached = await handler.cachePut(request, response.clone());\n                if (process.env.NODE_ENV !== 'production') {\n                    if (wasCached) {\n                        logger.log(`A response for ${getFriendlyURL(request.url)} ` +\n                            `was used to \"repair\" the precache.`);\n                    }\n                }\n            }\n        }\n        else {\n            // This shouldn't normally happen, but there are edge cases:\n            // https://github.com/GoogleChrome/workbox/issues/1441\n            throw new WorkboxError('missing-precache-entry', {\n                cacheName: this.cacheName,\n                url: request.url,\n            });\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            const cacheKey = params.cacheKey || (await handler.getCacheKey(request, 'read'));\n            // Workbox is going to handle the route.\n            // print the routing details to the console.\n            logger.groupCollapsed(`Precaching is responding to: ` + getFriendlyURL(request.url));\n            logger.log(`Serving the precached url: ${getFriendlyURL(cacheKey instanceof Request ? cacheKey.url : cacheKey)}`);\n            logger.groupCollapsed(`View request details here.`);\n            logger.log(request);\n            logger.groupEnd();\n            logger.groupCollapsed(`View response details here.`);\n            logger.log(response);\n            logger.groupEnd();\n            logger.groupEnd();\n        }\n        return response;\n    }\n    async _handleInstall(request, handler) {\n        this._useDefaultCacheabilityPluginIfNeeded();\n        const response = await handler.fetch(request);\n        // Make sure we defer cachePut() until after we know the response\n        // should be cached; see https://github.com/GoogleChrome/workbox/issues/2737\n        const wasCached = await handler.cachePut(request, response.clone());\n        if (!wasCached) {\n            // Throwing here will lead to the `install` handler failing, which\n            // we want to do if *any* of the responses aren't safe to cache.\n            throw new WorkboxError('bad-precaching-response', {\n                url: request.url,\n                status: response.status,\n            });\n        }\n        return response;\n    }\n    /**\n     * This method is complex, as there a number of things to account for:\n     *\n     * The `plugins` array can be set at construction, and/or it might be added to\n     * to at any time before the strategy is used.\n     *\n     * At the time the strategy is used (i.e. during an `install` event), there\n     * needs to be at least one plugin that implements `cacheWillUpdate` in the\n     * array, other than `copyRedirectedCacheableResponsesPlugin`.\n     *\n     * - If this method is called and there are no suitable `cacheWillUpdate`\n     * plugins, we need to add `defaultPrecacheCacheabilityPlugin`.\n     *\n     * - If this method is called and there is exactly one `cacheWillUpdate`, then\n     * we don't have to do anything (this might be a previously added\n     * `defaultPrecacheCacheabilityPlugin`, or it might be a custom plugin).\n     *\n     * - If this method is called and there is more than one `cacheWillUpdate`,\n     * then we need to check if one is `defaultPrecacheCacheabilityPlugin`. If so,\n     * we need to remove it. (This situation is unlikely, but it could happen if\n     * the strategy is used multiple times, the first without a `cacheWillUpdate`,\n     * and then later on after manually adding a custom `cacheWillUpdate`.)\n     *\n     * See https://github.com/GoogleChrome/workbox/issues/2737 for more context.\n     *\n     * @private\n     */\n    _useDefaultCacheabilityPluginIfNeeded() {\n        let defaultPluginIndex = null;\n        let cacheWillUpdatePluginCount = 0;\n        for (const [index, plugin] of this.plugins.entries()) {\n            // Ignore the copy redirected plugin when determining what to do.\n            if (plugin === PrecacheStrategy.copyRedirectedCacheableResponsesPlugin) {\n                continue;\n            }\n            // Save the default plugin's index, in case it needs to be removed.\n            if (plugin === PrecacheStrategy.defaultPrecacheCacheabilityPlugin) {\n                defaultPluginIndex = index;\n            }\n            if (plugin.cacheWillUpdate) {\n                cacheWillUpdatePluginCount++;\n            }\n        }\n        if (cacheWillUpdatePluginCount === 0) {\n            this.plugins.push(PrecacheStrategy.defaultPrecacheCacheabilityPlugin);\n        }\n        else if (cacheWillUpdatePluginCount > 1 && defaultPluginIndex !== null) {\n            // Only remove the default plugin; multiple custom plugins are allowed.\n            this.plugins.splice(defaultPluginIndex, 1);\n        }\n        // Nothing needs to be done if cacheWillUpdatePluginCount is 1\n    }\n}\nPrecacheStrategy.defaultPrecacheCacheabilityPlugin = {\n    async cacheWillUpdate({ response }) {\n        if (!response || response.status >= 400) {\n            return null;\n        }\n        return response;\n    },\n};\nPrecacheStrategy.copyRedirectedCacheableResponsesPlugin = {\n    async cacheWillUpdate({ response }) {\n        return response.redirected ? await copyResponse(response) : response;\n    },\n};\nexport { PrecacheStrategy };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { waitUntil } from 'workbox-core/_private/waitUntil.js';\nimport { createCacheKey } from './utils/createCacheKey.js';\nimport { PrecacheInstallReportPlugin } from './utils/PrecacheInstallReportPlugin.js';\nimport { PrecacheCacheKeyPlugin } from './utils/PrecacheCacheKeyPlugin.js';\nimport { printCleanupDetails } from './utils/printCleanupDetails.js';\nimport { printInstallDetails } from './utils/printInstallDetails.js';\nimport { PrecacheStrategy } from './PrecacheStrategy.js';\nimport './_version.js';\n/**\n * Performs efficient precaching of assets.\n *\n * @memberof workbox-precaching\n */\nclass PrecacheController {\n    /**\n     * Create a new PrecacheController.\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] The cache to use for precaching.\n     * @param {string} [options.plugins] Plugins to use when precaching as well\n     * as responding to fetch events for precached assets.\n     * @param {boolean} [options.fallbackToNetwork=true] Whether to attempt to\n     * get the response from the network if there's a precache miss.\n     */\n    constructor({ cacheName, plugins = [], fallbackToNetwork = true, } = {}) {\n        this._urlsToCacheKeys = new Map();\n        this._urlsToCacheModes = new Map();\n        this._cacheKeysToIntegrities = new Map();\n        this._strategy = new PrecacheStrategy({\n            cacheName: cacheNames.getPrecacheName(cacheName),\n            plugins: [\n                ...plugins,\n                new PrecacheCacheKeyPlugin({ precacheController: this }),\n            ],\n            fallbackToNetwork,\n        });\n        // Bind the install and activate methods to the instance.\n        this.install = this.install.bind(this);\n        this.activate = this.activate.bind(this);\n    }\n    /**\n     * @type {workbox-precaching.PrecacheStrategy} The strategy created by this controller and\n     * used to cache assets and respond to fetch events.\n     */\n    get strategy() {\n        return this._strategy;\n    }\n    /**\n     * Adds items to the precache list, removing any duplicates and\n     * stores the files in the\n     * {@link workbox-core.cacheNames|\"precache cache\"} when the service\n     * worker installs.\n     *\n     * This method can be called multiple times.\n     *\n     * @param {Array<Object|string>} [entries=[]] Array of entries to precache.\n     */\n    precache(entries) {\n        this.addToCacheList(entries);\n        if (!this._installAndActiveListenersAdded) {\n            self.addEventListener('install', this.install);\n            self.addEventListener('activate', this.activate);\n            this._installAndActiveListenersAdded = true;\n        }\n    }\n    /**\n     * This method will add items to the precache list, removing duplicates\n     * and ensuring the information is valid.\n     *\n     * @param {Array<workbox-precaching.PrecacheController.PrecacheEntry|string>} entries\n     *     Array of entries to precache.\n     */\n    addToCacheList(entries) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isArray(entries, {\n                moduleName: 'workbox-precaching',\n                className: 'PrecacheController',\n                funcName: 'addToCacheList',\n                paramName: 'entries',\n            });\n        }\n        const urlsToWarnAbout = [];\n        for (const entry of entries) {\n            // See https://github.com/GoogleChrome/workbox/issues/2259\n            if (typeof entry === 'string') {\n                urlsToWarnAbout.push(entry);\n            }\n            else if (entry && entry.revision === undefined) {\n                urlsToWarnAbout.push(entry.url);\n            }\n            const { cacheKey, url } = createCacheKey(entry);\n            const cacheMode = typeof entry !== 'string' && entry.revision ? 'reload' : 'default';\n            if (this._urlsToCacheKeys.has(url) &&\n                this._urlsToCacheKeys.get(url) !== cacheKey) {\n                throw new WorkboxError('add-to-cache-list-conflicting-entries', {\n                    firstEntry: this._urlsToCacheKeys.get(url),\n                    secondEntry: cacheKey,\n                });\n            }\n            if (typeof entry !== 'string' && entry.integrity) {\n                if (this._cacheKeysToIntegrities.has(cacheKey) &&\n                    this._cacheKeysToIntegrities.get(cacheKey) !== entry.integrity) {\n                    throw new WorkboxError('add-to-cache-list-conflicting-integrities', {\n                        url,\n                    });\n                }\n                this._cacheKeysToIntegrities.set(cacheKey, entry.integrity);\n            }\n            this._urlsToCacheKeys.set(url, cacheKey);\n            this._urlsToCacheModes.set(url, cacheMode);\n            if (urlsToWarnAbout.length > 0) {\n                const warningMessage = `Workbox is precaching URLs without revision ` +\n                    `info: ${urlsToWarnAbout.join(', ')}\\nThis is generally NOT safe. ` +\n                    `Learn more at https://bit.ly/wb-precache`;\n                if (process.env.NODE_ENV === 'production') {\n                    // Use console directly to display this warning without bloating\n                    // bundle sizes by pulling in all of the logger codebase in prod.\n                    console.warn(warningMessage);\n                }\n                else {\n                    logger.warn(warningMessage);\n                }\n            }\n        }\n    }\n    /**\n     * Precaches new and updated assets. Call this method from the service worker\n     * install event.\n     *\n     * Note: this method calls `event.waitUntil()` for you, so you do not need\n     * to call it yourself in your event handlers.\n     *\n     * @param {ExtendableEvent} event\n     * @return {Promise<workbox-precaching.InstallResult>}\n     */\n    install(event) {\n        // waitUntil returns Promise<any>\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return waitUntil(event, async () => {\n            const installReportPlugin = new PrecacheInstallReportPlugin();\n            this.strategy.plugins.push(installReportPlugin);\n            // Cache entries one at a time.\n            // See https://github.com/GoogleChrome/workbox/issues/2528\n            for (const [url, cacheKey] of this._urlsToCacheKeys) {\n                const integrity = this._cacheKeysToIntegrities.get(cacheKey);\n                const cacheMode = this._urlsToCacheModes.get(url);\n                const request = new Request(url, {\n                    integrity,\n                    cache: cacheMode,\n                    credentials: 'same-origin',\n                });\n                await Promise.all(this.strategy.handleAll({\n                    params: { cacheKey },\n                    request,\n                    event,\n                }));\n            }\n            const { updatedURLs, notUpdatedURLs } = installReportPlugin;\n            if (process.env.NODE_ENV !== 'production') {\n                printInstallDetails(updatedURLs, notUpdatedURLs);\n            }\n            return { updatedURLs, notUpdatedURLs };\n        });\n    }\n    /**\n     * Deletes assets that are no longer present in the current precache manifest.\n     * Call this method from the service worker activate event.\n     *\n     * Note: this method calls `event.waitUntil()` for you, so you do not need\n     * to call it yourself in your event handlers.\n     *\n     * @param {ExtendableEvent} event\n     * @return {Promise<workbox-precaching.CleanupResult>}\n     */\n    activate(event) {\n        // waitUntil returns Promise<any>\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return waitUntil(event, async () => {\n            const cache = await self.caches.open(this.strategy.cacheName);\n            const currentlyCachedRequests = await cache.keys();\n            const expectedCacheKeys = new Set(this._urlsToCacheKeys.values());\n            const deletedURLs = [];\n            for (const request of currentlyCachedRequests) {\n                if (!expectedCacheKeys.has(request.url)) {\n                    await cache.delete(request);\n                    deletedURLs.push(request.url);\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                printCleanupDetails(deletedURLs);\n            }\n            return { deletedURLs };\n        });\n    }\n    /**\n     * Returns a mapping of a precached URL to the corresponding cache key, taking\n     * into account the revision information for the URL.\n     *\n     * @return {Map<string, string>} A URL to cache key mapping.\n     */\n    getURLsToCacheKeys() {\n        return this._urlsToCacheKeys;\n    }\n    /**\n     * Returns a list of all the URLs that have been precached by the current\n     * service worker.\n     *\n     * @return {Array<string>} The precached URLs.\n     */\n    getCachedURLs() {\n        return [...this._urlsToCacheKeys.keys()];\n    }\n    /**\n     * Returns the cache key used for storing a given URL. If that URL is\n     * unversioned, like `/index.html', then the cache key will be the original\n     * URL with a search parameter appended to it.\n     *\n     * @param {string} url A URL whose cache key you want to look up.\n     * @return {string} The versioned URL that corresponds to a cache key\n     * for the original URL, or undefined if that URL isn't precached.\n     */\n    getCacheKeyForURL(url) {\n        const urlObject = new URL(url, location.href);\n        return this._urlsToCacheKeys.get(urlObject.href);\n    }\n    /**\n     * @param {string} url A cache key whose SRI you want to look up.\n     * @return {string} The subresource integrity associated with the cache key,\n     * or undefined if it's not set.\n     */\n    getIntegrityForCacheKey(cacheKey) {\n        return this._cacheKeysToIntegrities.get(cacheKey);\n    }\n    /**\n     * This acts as a drop-in replacement for\n     * [`cache.match()`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/match)\n     * with the following differences:\n     *\n     * - It knows what the name of the precache is, and only checks in that cache.\n     * - It allows you to pass in an \"original\" URL without versioning parameters,\n     * and it will automatically look up the correct cache key for the currently\n     * active revision of that URL.\n     *\n     * E.g., `matchPrecache('index.html')` will find the correct precached\n     * response for the currently active service worker, even if the actual cache\n     * key is `'/index.html?__WB_REVISION__=1234abcd'`.\n     *\n     * @param {string|Request} request The key (without revisioning parameters)\n     * to look up in the precache.\n     * @return {Promise<Response|undefined>}\n     */\n    async matchPrecache(request) {\n        const url = request instanceof Request ? request.url : request;\n        const cacheKey = this.getCacheKeyForURL(url);\n        if (cacheKey) {\n            const cache = await self.caches.open(this.strategy.cacheName);\n            return cache.match(cacheKey);\n        }\n        return undefined;\n    }\n    /**\n     * Returns a function that looks up `url` in the precache (taking into\n     * account revision information), and returns the corresponding `Response`.\n     *\n     * @param {string} url The precached URL which will be used to lookup the\n     * `Response`.\n     * @return {workbox-routing~handlerCallback}\n     */\n    createHandlerBoundToURL(url) {\n        const cacheKey = this.getCacheKeyForURL(url);\n        if (!cacheKey) {\n            throw new WorkboxError('non-precached-url', { url });\n        }\n        return (options) => {\n            options.request = new Request(url);\n            options.params = Object.assign({ cacheKey }, options.params);\n            return this.strategy.handle(options);\n        };\n    }\n}\nexport { PrecacheController };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { PrecacheController } from '../PrecacheController.js';\nimport '../_version.js';\nlet precacheController;\n/**\n * @return {PrecacheController}\n * @private\n */\nexport const getOrCreatePrecacheController = () => {\n    if (!precacheController) {\n        precacheController = new PrecacheController();\n    }\n    return precacheController;\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Removes any URL search parameters that should be ignored.\n *\n * @param {URL} urlObject The original URL.\n * @param {Array<RegExp>} ignoreURLParametersMatching RegExps to test against\n * each search parameter name. Matches mean that the search parameter should be\n * ignored.\n * @return {URL} The URL with any ignored search parameters removed.\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function removeIgnoredSearchParams(urlObject, ignoreURLParametersMatching = []) {\n    // Convert the iterable into an array at the start of the loop to make sure\n    // deletion doesn't mess up iteration.\n    for (const paramName of [...urlObject.searchParams.keys()]) {\n        if (ignoreURLParametersMatching.some((regExp) => regExp.test(paramName))) {\n            urlObject.searchParams.delete(paramName);\n        }\n    }\n    return urlObject;\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { removeIgnoredSearchParams } from './removeIgnoredSearchParams.js';\nimport '../_version.js';\n/**\n * Generator function that yields possible variations on the original URL to\n * check, one at a time.\n *\n * @param {string} url\n * @param {Object} options\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function* generateURLVariations(url, { ignoreURLParametersMatching = [/^utm_/, /^fbclid$/], directoryIndex = 'index.html', cleanURLs = true, urlManipulation, } = {}) {\n    const urlObject = new URL(url, location.href);\n    urlObject.hash = '';\n    yield urlObject.href;\n    const urlWithoutIgnoredParams = removeIgnoredSearchParams(urlObject, ignoreURLParametersMatching);\n    yield urlWithoutIgnoredParams.href;\n    if (directoryIndex && urlWithoutIgnoredParams.pathname.endsWith('/')) {\n        const directoryURL = new URL(urlWithoutIgnoredParams.href);\n        directoryURL.pathname += directoryIndex;\n        yield directoryURL.href;\n    }\n    if (cleanURLs) {\n        const cleanURL = new URL(urlWithoutIgnoredParams.href);\n        cleanURL.pathname += '.html';\n        yield cleanURL.href;\n    }\n    if (urlManipulation) {\n        const additionalURLs = urlManipulation({ url: urlObject });\n        for (const urlToAttempt of additionalURLs) {\n            yield urlToAttempt.href;\n        }\n    }\n}\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { Route } from 'workbox-routing/Route.js';\nimport { generateURLVariations } from './utils/generateURLVariations.js';\nimport './_version.js';\n/**\n * A subclass of {@link workbox-routing.Route} that takes a\n * {@link workbox-precaching.PrecacheController}\n * instance and uses it to match incoming requests and handle fetching\n * responses from the precache.\n *\n * @memberof workbox-precaching\n * @extends workbox-routing.Route\n */\nclass PrecacheRoute extends Route {\n    /**\n     * @param {PrecacheController} precacheController A `PrecacheController`\n     * instance used to both match requests and respond to fetch events.\n     * @param {Object} [options] Options to control how requests are matched\n     * against the list of precached URLs.\n     * @param {string} [options.directoryIndex=index.html] The `directoryIndex` will\n     * check cache entries for a URLs ending with '/' to see if there is a hit when\n     * appending the `directoryIndex` value.\n     * @param {Array<RegExp>} [options.ignoreURLParametersMatching=[/^utm_/, /^fbclid$/]] An\n     * array of regex's to remove search params when looking for a cache match.\n     * @param {boolean} [options.cleanURLs=true] The `cleanURLs` option will\n     * check the cache for the URL with a `.html` added to the end of the end.\n     * @param {workbox-precaching~urlManipulation} [options.urlManipulation]\n     * This is a function that should take a URL and return an array of\n     * alternative URLs that should be checked for precache matches.\n     */\n    constructor(precacheController, options) {\n        const match = ({ request, }) => {\n            const urlsToCacheKeys = precacheController.getURLsToCacheKeys();\n            for (const possibleURL of generateURLVariations(request.url, options)) {\n                const cacheKey = urlsToCacheKeys.get(possibleURL);\n                if (cacheKey) {\n                    const integrity = precacheController.getIntegrityForCacheKey(cacheKey);\n                    return { cacheKey, integrity };\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Precaching did not find a match for ` + getFriendlyURL(request.url));\n            }\n            return;\n        };\n        super(match, precacheController.strategy);\n    }\n}\nexport { PrecacheRoute };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport { PrecacheRoute } from './PrecacheRoute.js';\nimport './_version.js';\n/**\n * Add a `fetch` listener to the service worker that will\n * respond to\n * [network requests]{@link https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API/Using_Service_Workers#Custom_responses_to_requests}\n * with precached assets.\n *\n * Requests for assets that aren't precached, the `FetchEvent` will not be\n * responded to, allowing the event to fall through to other `fetch` event\n * listeners.\n *\n * @param {Object} [options] See the {@link workbox-precaching.PrecacheRoute}\n * options.\n *\n * @memberof workbox-precaching\n */\nfunction addRoute(options) {\n    const precacheController = getOrCreatePrecacheController();\n    const precacheRoute = new PrecacheRoute(precacheController, options);\n    registerRoute(precacheRoute);\n}\nexport { addRoute };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Adds items to the precache list, removing any duplicates and\n * stores the files in the\n * {@link workbox-core.cacheNames|\"precache cache\"} when the service\n * worker installs.\n *\n * This method can be called multiple times.\n *\n * Please note: This method **will not** serve any of the cached files for you.\n * It only precaches files. To respond to a network request you call\n * {@link workbox-precaching.addRoute}.\n *\n * If you have a single array of files to precache, you can just call\n * {@link workbox-precaching.precacheAndRoute}.\n *\n * @param {Array<Object|string>} [entries=[]] Array of entries to precache.\n *\n * @memberof workbox-precaching\n */\nfunction precache(entries) {\n    const precacheController = getOrCreatePrecacheController();\n    precacheController.precache(entries);\n}\nexport { precache };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { addRoute } from './addRoute.js';\nimport { precache } from './precache.js';\nimport './_version.js';\n/**\n * This method will add entries to the precache list and add a route to\n * respond to fetch events.\n *\n * This is a convenience method that will call\n * {@link workbox-precaching.precache} and\n * {@link workbox-precaching.addRoute} in a single call.\n *\n * @param {Array<Object|string>} entries Array of entries to precache.\n * @param {Object} [options] See the\n * {@link workbox-precaching.PrecacheRoute} options.\n *\n * @memberof workbox-precaching\n */\nfunction precacheAndRoute(entries, options) {\n    precache(entries);\n    addRoute(options);\n}\nexport { precacheAndRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst SUBSTRING_TO_FIND = '-precache-';\n/**\n * Cleans up incompatible precaches that were created by older versions of\n * Workbox, by a service worker registered under the current scope.\n *\n * This is meant to be called as part of the `activate` event.\n *\n * This should be safe to use as long as you don't include `substringToFind`\n * (defaulting to `-precache-`) in your non-precache cache names.\n *\n * @param {string} currentPrecacheName The cache name currently in use for\n * precaching. This cache won't be deleted.\n * @param {string} [substringToFind='-precache-'] Cache names which include this\n * substring will be deleted (excluding `currentPrecacheName`).\n * @return {Array<string>} A list of all the cache names that were deleted.\n *\n * @private\n * @memberof workbox-precaching\n */\nconst deleteOutdatedCaches = async (currentPrecacheName, substringToFind = SUBSTRING_TO_FIND) => {\n    const cacheNames = await self.caches.keys();\n    const cacheNamesToDelete = cacheNames.filter((cacheName) => {\n        return (cacheName.includes(substringToFind) &&\n            cacheName.includes(self.registration.scope) &&\n            cacheName !== currentPrecacheName);\n    });\n    await Promise.all(cacheNamesToDelete.map((cacheName) => self.caches.delete(cacheName)));\n    return cacheNamesToDelete;\n};\nexport { deleteOutdatedCaches };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { deleteOutdatedCaches } from './utils/deleteOutdatedCaches.js';\nimport './_version.js';\n/**\n * Adds an `activate` event listener which will clean up incompatible\n * precaches that were created by older versions of Workbox.\n *\n * @memberof workbox-precaching\n */\nfunction cleanupOutdatedCaches() {\n    // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n    self.addEventListener('activate', ((event) => {\n        const cacheName = cacheNames.getPrecacheName();\n        event.waitUntil(deleteOutdatedCaches(cacheName).then((cachesDeleted) => {\n            if (process.env.NODE_ENV !== 'production') {\n                if (cachesDeleted.length > 0) {\n                    logger.log(`The following out-of-date precaches were cleaned up ` +\n                        `automatically:`, cachesDeleted);\n                }\n            }\n        }));\n    }));\n}\nexport { cleanupOutdatedCaches };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { Route } from './Route.js';\nimport './_version.js';\n/**\n * NavigationRoute makes it easy to create a\n * {@link workbox-routing.Route} that matches for browser\n * [navigation requests]{@link https://developers.google.com/web/fundamentals/primers/service-workers/high-performance-loading#first_what_are_navigation_requests}.\n *\n * It will only match incoming Requests whose\n * {@link https://fetch.spec.whatwg.org/#concept-request-mode|mode}\n * is set to `navigate`.\n *\n * You can optionally only apply this route to a subset of navigation requests\n * by using one or both of the `denylist` and `allowlist` parameters.\n *\n * @memberof workbox-routing\n * @extends workbox-routing.Route\n */\nclass NavigationRoute extends Route {\n    /**\n     * If both `denylist` and `allowlist` are provided, the `denylist` will\n     * take precedence and the request will not match this route.\n     *\n     * The regular expressions in `allowlist` and `denylist`\n     * are matched against the concatenated\n     * [`pathname`]{@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLHyperlinkElementUtils/pathname}\n     * and [`search`]{@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLHyperlinkElementUtils/search}\n     * portions of the requested URL.\n     *\n     * *Note*: These RegExps may be evaluated against every destination URL during\n     * a navigation. Avoid using\n     * [complex RegExps](https://github.com/GoogleChrome/workbox/issues/3077),\n     * or else your users may see delays when navigating your site.\n     *\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {Object} options\n     * @param {Array<RegExp>} [options.denylist] If any of these patterns match,\n     * the route will not handle the request (even if a allowlist RegExp matches).\n     * @param {Array<RegExp>} [options.allowlist=[/./]] If any of these patterns\n     * match the URL's pathname and search parameter, the route will handle the\n     * request (assuming the denylist doesn't match).\n     */\n    constructor(handler, { allowlist = [/./], denylist = [] } = {}) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isArrayOfClass(allowlist, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'NavigationRoute',\n                funcName: 'constructor',\n                paramName: 'options.allowlist',\n            });\n            assert.isArrayOfClass(denylist, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'NavigationRoute',\n                funcName: 'constructor',\n                paramName: 'options.denylist',\n            });\n        }\n        super((options) => this._match(options), handler);\n        this._allowlist = allowlist;\n        this._denylist = denylist;\n    }\n    /**\n     * Routes match handler.\n     *\n     * @param {Object} options\n     * @param {URL} options.url\n     * @param {Request} options.request\n     * @return {boolean}\n     *\n     * @private\n     */\n    _match({ url, request }) {\n        if (request && request.mode !== 'navigate') {\n            return false;\n        }\n        const pathnameAndSearch = url.pathname + url.search;\n        for (const regExp of this._denylist) {\n            if (regExp.test(pathnameAndSearch)) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`The navigation route ${pathnameAndSearch} is not ` +\n                        `being used, since the URL matches this denylist pattern: ` +\n                        `${regExp.toString()}`);\n                }\n                return false;\n            }\n        }\n        if (this._allowlist.some((regExp) => regExp.test(pathnameAndSearch))) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`The navigation route ${pathnameAndSearch} ` + `is being used.`);\n            }\n            return true;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`The navigation route ${pathnameAndSearch} is not ` +\n                `being used, since the URL being navigated to doesn't ` +\n                `match the allowlist.`);\n        }\n        return false;\n    }\n}\nexport { NavigationRoute };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Helper function that calls\n * {@link PrecacheController#createHandlerBoundToURL} on the default\n * {@link PrecacheController} instance.\n *\n * If you are creating your own {@link PrecacheController}, then call the\n * {@link PrecacheController#createHandlerBoundToURL} on that instance,\n * instead of using this function.\n *\n * @param {string} url The precached URL which will be used to lookup the\n * `Response`.\n * @param {boolean} [fallbackToNetwork=true] Whether to attempt to get the\n * response from the network if there's a precache miss.\n * @return {workbox-routing~handlerCallback}\n *\n * @memberof workbox-precaching\n */\nfunction createHandlerBoundToURL(url) {\n    const precacheController = getOrCreatePrecacheController();\n    return precacheController.createHandlerBoundToURL(url);\n}\nexport { createHandlerBoundToURL };\n"], "names": ["self", "_", "e", "logger", "globalThis", "__WB_DISABLE_DEV_LOGS", "inGroup", "methodToColorMap", "debug", "log", "warn", "error", "groupCollapsed", "groupEnd", "print", "method", "args", "test", "navigator", "userAgent", "console", "styles", "logPrefix", "join", "api", "loggerMethods", "Object", "keys", "key", "messages", "invalid-value", "paramName", "validValueDescription", "value", "Error", "JSON", "stringify", "not-an-array", "moduleName", "className", "funcName", "incorrect-type", "expectedType", "classNameStr", "incorrect-class", "expectedClassName", "isReturnValueProblem", "missing-a-method", "<PERSON><PERSON><PERSON><PERSON>", "add-to-cache-list-unexpected-type", "entry", "add-to-cache-list-conflicting-entries", "firstEntry", "secondEntry", "plugin-error-request-will-fetch", "thrownErrorMessage", "invalid-cache-name", "cacheNameId", "unregister-route-but-not-found-with-method", "unregister-route-route-not-registered", "queue-replay-failed", "name", "duplicate-queue-name", "expired-test-without-max-age", "methodName", "unsupported-route-type", "not-array-of-class", "expectedClass", "max-entries-or-age-required", "statuses-or-headers-required", "invalid-string", "channel-name-required", "invalid-responses-are-same-args", "expire-custom-caches-only", "unit-must-be-bytes", "normalizedRangeHeader", "single-range-only", "invalid-range-values", "no-range-header", "range-not-satisfiable", "size", "start", "end", "attempt-to-cache-non-get-request", "url", "cache-put-with-no-response", "no-response", "message", "bad-precaching-response", "status", "non-precached-url", "add-to-cache-list-conflicting-integrities", "missing-precache-entry", "cacheName", "cross-origin-copy-response", "origin", "opaque-streams-source", "type", "generatorFunction", "code", "details", "messageGenerator", "WorkboxError", "constructor", "errorCode", "isArray", "Array", "has<PERSON><PERSON><PERSON>", "object", "isType", "isInstance", "isOneOf", "validValues", "includes", "isArrayOfClass", "item", "finalAssertExports", "defaultMethod", "validMethods", "normalize<PERSON><PERSON><PERSON>", "handler", "assert", "handle", "Route", "match", "setCatchHandler", "<PERSON><PERSON><PERSON><PERSON>", "RegExpRoute", "regExp", "RegExp", "result", "exec", "href", "location", "index", "toString", "slice", "getFriendlyURL", "url<PERSON>bj", "URL", "String", "replace", "Router", "_routes", "Map", "_defaultHandlerMap", "routes", "addFetchListener", "addEventListener", "event", "request", "responsePromise", "handleRequest", "respondWith", "addCacheListener", "data", "payload", "urlsToCache", "requestPromises", "Promise", "all", "map", "Request", "waitUntil", "ports", "then", "postMessage", "protocol", "startsWith", "<PERSON><PERSON><PERSON><PERSON>", "params", "route", "findMatchingRoute", "debugMessages", "push", "has", "get", "for<PERSON>ach", "msg", "err", "reject", "_catch<PERSON><PERSON>ler", "catch", "catchErr", "matchResult", "length", "undefined", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "set", "registerRoute", "unregisterRoute", "routeIndex", "indexOf", "splice", "defaultRouter", "getOrCreateDefaultRouter", "capture", "captureUrl", "valueToCheck", "pathname", "wildcards", "matchCallback", "_cacheNameDetails", "googleAnalytics", "precache", "prefix", "runtime", "suffix", "registration", "scope", "_createCacheName", "filter", "eachCacheNameDetail", "fn", "cacheNames", "updateDetails", "getGoogleAnalyticsName", "userCacheName", "getPrecacheName", "getPrefix", "getRuntimeName", "getSuffix", "dontWait<PERSON>or", "promise", "quotaErrorCallbacks", "Set", "registerQuotaErrorCallback", "callback", "add", "instanceOfAny", "constructors", "some", "c", "idbProxyableTypes", "cursorAdvanceMethods", "getIdbProxyableTypes", "IDBDatabase", "IDBObjectStore", "IDBIndex", "IDBCursor", "IDBTransaction", "getCursorAdvanceMethods", "prototype", "advance", "continue", "continuePrimaryKey", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "promisifyRequest", "resolve", "unlisten", "removeEventListener", "success", "wrap", "cacheDonePromiseForTransaction", "tx", "done", "complete", "DOMException", "idbProxyTraps", "target", "prop", "receiver", "objectStoreNames", "objectStore", "replaceTraps", "wrapFunction", "func", "transaction", "storeNames", "call", "unwrap", "sort", "apply", "transformCachableValue", "Proxy", "IDBRequest", "newValue", "openDB", "version", "blocked", "upgrade", "blocking", "terminated", "indexedDB", "open", "openPromise", "oldVersion", "newVersion", "db", "deleteDB", "deleteDatabase", "readMethods", "writeMethods", "cachedMethods", "getMethod", "targetFuncName", "useIndex", "isWrite", "storeName", "store", "shift", "oldTraps", "_extends", "DB_NAME", "CACHE_OBJECT_STORE", "normalizeURL", "unNormalizedUrl", "hash", "CacheTimestampsModel", "_db", "_cacheName", "_upgradeDb", "objStore", "createObjectStore", "keyP<PERSON>", "createIndex", "unique", "_upgradeDbAndDeleteOldDbs", "setTimestamp", "timestamp", "id", "_getId", "getDb", "durability", "put", "getTimestamp", "expireEntries", "minTimestamp", "maxCount", "cursor", "openCursor", "entriesToDelete", "entriesNotDeletedCount", "urlsDeleted", "delete", "bind", "CacheExpiration", "config", "_isRunning", "_rerunRequested", "maxEntries", "maxAgeSeconds", "_maxEntries", "_maxAgeSeconds", "_matchOptions", "matchOptions", "_timestampModel", "Date", "now", "urlsExpired", "cache", "caches", "updateTimestamp", "isURLExpired", "expire<PERSON><PERSON><PERSON><PERSON>", "Infinity", "ExpirationPlugin", "cachedResponseWillBeUsed", "cachedResponse", "isFresh", "_isResponseDateFresh", "cacheExpiration", "_getCacheExpiration", "updateTimestampDone", "cacheDidUpdate", "_config", "_cacheExpirations", "purgeOnQuotaError", "deleteCacheAndMetadata", "dateHeaderTimestamp", "_getDateHeaderTimestamp", "headers", "<PERSON><PERSON><PERSON><PERSON>", "parsedDate", "headerTime", "getTime", "isNaN", "CacheableResponse", "statuses", "_statuses", "_headers", "isResponseCacheable", "response", "Response", "cacheable", "headerName", "logFriendlyHeaders", "CacheableResponsePlugin", "cacheWillUpdate", "_cacheableResponse", "stripParams", "fullURL", "ignoreParams", "strippedURL", "param", "searchParams", "cacheMatchIgnoreParams", "strippedRequestURL", "keysOptions", "assign", "ignoreSearch", "cacheKeys", "cache<PERSON>ey", "strippedCacheKeyURL", "Deferred", "executeQuotaErrorCallbacks", "timeout", "ms", "setTimeout", "toRequest", "input", "StrategyHandler", "strategy", "options", "_cacheKeys", "ExtendableEvent", "_strategy", "_handler<PERSON><PERSON><PERSON><PERSON>", "_extendLifetimePromises", "_plugins", "plugins", "_pluginStateMap", "plugin", "fetch", "mode", "FetchEvent", "preloadResponse", "possiblePreloadResponse", "originalRequest", "<PERSON><PERSON><PERSON><PERSON>", "clone", "cb", "iterateCallbacks", "pluginFilteredRequest", "fetchResponse", "fetchOptions", "runCallbacks", "fetchAndCachePut", "responseClone", "cachePut", "cacheMatch", "effectiveRequest", "get<PERSON><PERSON><PERSON><PERSON>", "multiMatchOptions", "vary", "responseToCache", "_ensureResponseSafeToCache", "hasCacheUpdateCallback", "oldResponse", "newResponse", "state", "stateful<PERSON><PERSON><PERSON>", "statefulParam", "doneWaiting", "destroy", "pluginsUsed", "Strategy", "responseDone", "handleAll", "_getResponse", "handlerDone", "_awaitComplete", "_handle", "waitUntilError", "strategyStart", "strategyName", "printFinalResponse", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "logs", "cacheOkAndOpaquePlugin", "NetworkFirst", "p", "unshift", "_networkTimeoutSeconds", "networkTimeoutSeconds", "promises", "timeoutId", "_getTimeoutPromise", "networkPromise", "_getNetworkPromise", "race", "timeoutPromise", "onNetworkTimeout", "fetchError", "clearTimeout", "clientsClaim", "clients", "claim", "asyncFn", "returnPromise", "REVISION_SEARCH_PARAM", "createCacheKey", "urlObject", "revision", "cacheKeyURL", "originalURL", "PrecacheInstallReportPlugin", "updatedURLs", "notUpdatedURLs", "handlerWillStart", "PrecacheCacheKeyPlugin", "precacheController", "cacheKeyWillBeUsed", "_precacheController", "getCacheKeyForURL", "logGroup", "groupTitle", "deletedURLs", "printCleanupDetails", "deletionCount", "_nestedGroup", "urls", "printInstallDetails", "urlsToPrecache", "urlsAlreadyPrecached", "precachedCount", "alreadyPrecachedCount", "supportStatus", "canConstructResponseFromBodyStream", "testResponse", "body", "copyResponse", "modifier", "responseURL", "clonedResponse", "responseInit", "Headers", "statusText", "modifiedResponseInit", "blob", "PrecacheStrategy", "_fallbackToNetwork", "fallbackToNetwork", "copyRedirectedCacheableResponsesPlugin", "_handleInstall", "_handleFetch", "integrityInManifest", "integrity", "integrityInRequest", "noIntegrityConflict", "_useDefaultCacheabilityPluginIfNeeded", "was<PERSON>ached", "defaultPluginIndex", "cacheWillUpdatePluginCount", "entries", "defaultPrecacheCacheabilityPlugin", "redirected", "PrecacheController", "_urlsTo<PERSON><PERSON><PERSON><PERSON><PERSON>", "_urlsToCacheModes", "_cacheKeysToIntegrities", "install", "activate", "addToCacheList", "_installAndActiveListenersAdded", "urlsToWarnAbout", "cacheMode", "warningMessage", "installReportPlugin", "credentials", "currentlyCachedRequests", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "values", "getURLsToCacheKeys", "getCachedURLs", "getIntegrityForCacheKey", "matchPrecache", "createHandlerBoundToURL", "getOrCreatePrecacheController", "removeIgnoredSearchParams", "ignoreURLParametersMatching", "generateURLVariations", "directoryIndex", "cleanURLs", "urlManipulation", "urlWithoutIgnoredParams", "endsWith", "directoryURL", "cleanURL", "additionalURLs", "urlToAttempt", "PrecacheRoute", "urlsTo<PERSON>ache<PERSON><PERSON>s", "possibleURL", "addRoute", "precacheRoute", "precacheAndRoute", "SUBSTRING_TO_FIND", "deleteOutdatedCaches", "currentPrecacheName", "substringToFind", "cacheNamesToDelete", "cleanupOutdatedCaches", "cachesDeleted", "NavigationRoute", "allowlist", "denylist", "_match", "_allowlist", "_denylist", "pathnameAndSearch", "search"], "mappings": ";;IACA;IACA,IAAI;IACAA,EAAAA,IAAI,CAAC,oBAAoB,CAAC,IAAIC,CAAC,EAAE,CAAA;IACrC,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;IACA;IACA;IACA;IACA;IAEA,MAAMC,MAAM,GAEN,CAAC,MAAM;IACL;IACA;IACA,EAAA,IAAI,EAAE,uBAAuB,IAAIC,UAAU,CAAC,EAAE;QAC1CJ,IAAI,CAACK,qBAAqB,GAAG,KAAK,CAAA;IACtC,GAAA;MACA,IAAIC,OAAO,GAAG,KAAK,CAAA;IACnB,EAAA,MAAMC,gBAAgB,GAAG;IACrBC,IAAAA,KAAK,EAAE,CAAS,OAAA,CAAA;IAChBC,IAAAA,GAAG,EAAE,CAAS,OAAA,CAAA;IACdC,IAAAA,IAAI,EAAE,CAAS,OAAA,CAAA;IACfC,IAAAA,KAAK,EAAE,CAAS,OAAA,CAAA;IAChBC,IAAAA,cAAc,EAAE,CAAS,OAAA,CAAA;QACzBC,QAAQ,EAAE,IAAI;OACjB,CAAA;IACD,EAAA,MAAMC,KAAK,GAAG,UAAUC,MAAM,EAAEC,IAAI,EAAE;QAClC,IAAIhB,IAAI,CAACK,qBAAqB,EAAE;IAC5B,MAAA,OAAA;IACJ,KAAA;QACA,IAAIU,MAAM,KAAK,gBAAgB,EAAE;IAC7B;IACA;UACA,IAAI,gCAAgC,CAACE,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,EAAE;IAC5DC,QAAAA,OAAO,CAACL,MAAM,CAAC,CAAC,GAAGC,IAAI,CAAC,CAAA;IACxB,QAAA,OAAA;IACJ,OAAA;IACJ,KAAA;IACA,IAAA,MAAMK,MAAM,GAAG,CACX,CAAed,YAAAA,EAAAA,gBAAgB,CAACQ,MAAM,CAAC,CAAE,CAAA,EACzC,sBAAsB,EACtB,CAAA,YAAA,CAAc,EACd,CAAmB,iBAAA,CAAA,EACnB,oBAAoB,CACvB,CAAA;IACD;IACA,IAAA,MAAMO,SAAS,GAAGhB,OAAO,GAAG,EAAE,GAAG,CAAC,WAAW,EAAEe,MAAM,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;QAChEH,OAAO,CAACL,MAAM,CAAC,CAAC,GAAGO,SAAS,EAAE,GAAGN,IAAI,CAAC,CAAA;QACtC,IAAID,MAAM,KAAK,gBAAgB,EAAE;IAC7BT,MAAAA,OAAO,GAAG,IAAI,CAAA;IAClB,KAAA;QACA,IAAIS,MAAM,KAAK,UAAU,EAAE;IACvBT,MAAAA,OAAO,GAAG,KAAK,CAAA;IACnB,KAAA;OACH,CAAA;IACD;MACA,MAAMkB,GAAG,GAAG,EAAE,CAAA;IACd,EAAA,MAAMC,aAAa,GAAGC,MAAM,CAACC,IAAI,CAACpB,gBAAgB,CAAC,CAAA;IACnD,EAAA,KAAK,MAAMqB,GAAG,IAAIH,aAAa,EAAE;QAC7B,MAAMV,MAAM,GAAGa,GAAG,CAAA;IAClBJ,IAAAA,GAAG,CAACT,MAAM,CAAC,GAAG,CAAC,GAAGC,IAAI,KAAK;IACvBF,MAAAA,KAAK,CAACC,MAAM,EAAEC,IAAI,CAAC,CAAA;SACtB,CAAA;IACL,GAAA;IACA,EAAA,OAAOQ,GAAG,CAAA;IACd,CAAC,GAAI;;IC/DT;IACA;AACA;IACA;IACA;IACA;IACA;IAEO,MAAMK,UAAQ,GAAG;IACpB,EAAA,eAAe,EAAEC,CAAC;QAAEC,SAAS;QAAEC,qBAAqB;IAAEC,IAAAA,KAAAA;IAAM,GAAC,KAAK;IAC9D,IAAA,IAAI,CAACF,SAAS,IAAI,CAACC,qBAAqB,EAAE;IACtC,MAAA,MAAM,IAAIE,KAAK,CAAC,CAAA,0CAAA,CAA4C,CAAC,CAAA;IACjE,KAAA;IACA,IAAA,OAAQ,CAAQH,KAAAA,EAAAA,SAAS,CAAwC,sCAAA,CAAA,GAC7D,qBAAqBC,qBAAqB,CAAA,qBAAA,CAAuB,GACjE,CAAA,EAAGG,IAAI,CAACC,SAAS,CAACH,KAAK,CAAC,CAAG,CAAA,CAAA,CAAA;OAClC;IACD,EAAA,cAAc,EAAEI,CAAC;QAAEC,UAAU;QAAEC,SAAS;QAAEC,QAAQ;IAAET,IAAAA,SAAAA;IAAU,GAAC,KAAK;QAChE,IAAI,CAACO,UAAU,IAAI,CAACC,SAAS,IAAI,CAACC,QAAQ,IAAI,CAACT,SAAS,EAAE;IACtD,MAAA,MAAM,IAAIG,KAAK,CAAC,CAAA,yCAAA,CAA2C,CAAC,CAAA;IAChE,KAAA;QACA,OAAQ,CAAA,eAAA,EAAkBH,SAAS,CAAA,cAAA,CAAgB,GAC/C,CAAA,CAAA,EAAIO,UAAU,CAAIC,CAAAA,EAAAA,SAAS,CAAIC,CAAAA,EAAAA,QAAQ,CAAuB,qBAAA,CAAA,CAAA;OACrE;IACD,EAAA,gBAAgB,EAAEC,CAAC;QAAEC,YAAY;QAAEX,SAAS;QAAEO,UAAU;QAAEC,SAAS;IAAEC,IAAAA,QAAAA;IAAU,GAAC,KAAK;QACjF,IAAI,CAACE,YAAY,IAAI,CAACX,SAAS,IAAI,CAACO,UAAU,IAAI,CAACE,QAAQ,EAAE;IACzD,MAAA,MAAM,IAAIN,KAAK,CAAC,CAAA,2CAAA,CAA6C,CAAC,CAAA;IAClE,KAAA;QACA,MAAMS,YAAY,GAAGJ,SAAS,GAAG,GAAGA,SAAS,CAAA,CAAA,CAAG,GAAG,EAAE,CAAA;IACrD,IAAA,OAAQ,CAAkBR,eAAAA,EAAAA,SAAS,CAAgB,cAAA,CAAA,GAC/C,IAAIO,UAAU,CAAA,CAAA,EAAIK,YAAY,CAAA,CAAE,GAChC,CAAA,EAAGH,QAAQ,CAAA,oBAAA,EAAuBE,YAAY,CAAG,CAAA,CAAA,CAAA;OACxD;IACD,EAAA,iBAAiB,EAAEE,CAAC;QAAEC,iBAAiB;QAAEd,SAAS;QAAEO,UAAU;QAAEC,SAAS;QAAEC,QAAQ;IAAEM,IAAAA,oBAAAA;IAAsB,GAAC,KAAK;QAC7G,IAAI,CAACD,iBAAiB,IAAI,CAACP,UAAU,IAAI,CAACE,QAAQ,EAAE;IAChD,MAAA,MAAM,IAAIN,KAAK,CAAC,CAAA,4CAAA,CAA8C,CAAC,CAAA;IACnE,KAAA;QACA,MAAMS,YAAY,GAAGJ,SAAS,GAAG,GAAGA,SAAS,CAAA,CAAA,CAAG,GAAG,EAAE,CAAA;IACrD,IAAA,IAAIO,oBAAoB,EAAE;IACtB,MAAA,OAAQ,CAAwB,sBAAA,CAAA,GAC5B,CAAIR,CAAAA,EAAAA,UAAU,CAAIK,CAAAA,EAAAA,YAAY,CAAGH,EAAAA,QAAQ,CAAM,IAAA,CAAA,GAC/C,CAAgCK,6BAAAA,EAAAA,iBAAiB,CAAG,CAAA,CAAA,CAAA;IAC5D,KAAA;IACA,IAAA,OAAQ,CAAkBd,eAAAA,EAAAA,SAAS,CAAgB,cAAA,CAAA,GAC/C,IAAIO,UAAU,CAAA,CAAA,EAAIK,YAAY,CAAA,EAAGH,QAAQ,CAAA,IAAA,CAAM,GAC/C,CAAA,6BAAA,EAAgCK,iBAAiB,CAAG,CAAA,CAAA,CAAA;OAC3D;IACD,EAAA,kBAAkB,EAAEE,CAAC;QAAEC,cAAc;QAAEjB,SAAS;QAAEO,UAAU;QAAEC,SAAS;IAAEC,IAAAA,QAAAA;IAAU,GAAC,KAAK;IACrF,IAAA,IAAI,CAACQ,cAAc,IACf,CAACjB,SAAS,IACV,CAACO,UAAU,IACX,CAACC,SAAS,IACV,CAACC,QAAQ,EAAE;IACX,MAAA,MAAM,IAAIN,KAAK,CAAC,CAAA,6CAAA,CAA+C,CAAC,CAAA;IACpE,KAAA;IACA,IAAA,OAAQ,CAAGI,EAAAA,UAAU,CAAIC,CAAAA,EAAAA,SAAS,CAAIC,CAAAA,EAAAA,QAAQ,CAAkB,gBAAA,CAAA,GAC5D,CAAIT,CAAAA,EAAAA,SAAS,CAA4BiB,yBAAAA,EAAAA,cAAc,CAAW,SAAA,CAAA,CAAA;OACzE;IACD,EAAA,mCAAmC,EAAEC,CAAC;IAAEC,IAAAA,KAAAA;IAAM,GAAC,KAAK;IAChD,IAAA,OAAQ,CAAoC,kCAAA,CAAA,GACxC,CAAqE,mEAAA,CAAA,GACrE,IAAIf,IAAI,CAACC,SAAS,CAACc,KAAK,CAAC,CAAA,+CAAA,CAAiD,GAC1E,CAAA,oEAAA,CAAsE,GACtE,CAAkB,gBAAA,CAAA,CAAA;OACzB;IACD,EAAA,uCAAuC,EAAEC,CAAC;QAAEC,UAAU;IAAEC,IAAAA,WAAAA;IAAY,GAAC,KAAK;IACtE,IAAA,IAAI,CAACD,UAAU,IAAI,CAACC,WAAW,EAAE;IAC7B,MAAA,MAAM,IAAInB,KAAK,CAAC,CAAsB,oBAAA,CAAA,GAAG,8CAA8C,CAAC,CAAA;IAC5F,KAAA;QACA,OAAQ,CAAA,6BAAA,CAA+B,GACnC,CAAA,qEAAA,CAAuE,GACvE,CAAA,EAAGkB,UAAU,CAA8C,4CAAA,CAAA,GAC3D,CAAqE,mEAAA,CAAA,GACrE,CAAiB,eAAA,CAAA,CAAA;OACxB;IACD,EAAA,iCAAiC,EAAEE,CAAC;IAAEC,IAAAA,kBAAAA;IAAmB,GAAC,KAAK;QAC3D,IAAI,CAACA,kBAAkB,EAAE;IACrB,MAAA,MAAM,IAAIrB,KAAK,CAAC,CAAsB,oBAAA,CAAA,GAAG,2CAA2C,CAAC,CAAA;IACzF,KAAA;IACA,IAAA,OAAQ,CAAgE,8DAAA,CAAA,GACpE,CAAkCqB,+BAAAA,EAAAA,kBAAkB,CAAI,EAAA,CAAA,CAAA;OAC/D;IACD,EAAA,oBAAoB,EAAEC,CAAC;QAAEC,WAAW;IAAExB,IAAAA,KAAAA;IAAM,GAAC,KAAK;QAC9C,IAAI,CAACwB,WAAW,EAAE;IACd,MAAA,MAAM,IAAIvB,KAAK,CAAC,CAAA,uDAAA,CAAyD,CAAC,CAAA;IAC9E,KAAA;IACA,IAAA,OAAQ,CAAgE,8DAAA,CAAA,GACpE,CAAoBuB,iBAAAA,EAAAA,WAAW,CAAiC,+BAAA,CAAA,GAChE,CAAItB,CAAAA,EAAAA,IAAI,CAACC,SAAS,CAACH,KAAK,CAAC,CAAG,CAAA,CAAA,CAAA;OACnC;IACD,EAAA,4CAA4C,EAAEyB,CAAC;IAAE3C,IAAAA,MAAAA;IAAO,GAAC,KAAK;QAC1D,IAAI,CAACA,MAAM,EAAE;IACT,MAAA,MAAM,IAAImB,KAAK,CAAC,CAAsB,oBAAA,CAAA,GAClC,qDAAqD,CAAC,CAAA;IAC9D,KAAA;IACA,IAAA,OAAQ,CAA4D,0DAAA,CAAA,GAChE,CAAmCnB,gCAAAA,EAAAA,MAAM,CAAI,EAAA,CAAA,CAAA;OACpD;MACD,uCAAuC,EAAE4C,MAAM;QAC3C,OAAQ,CAAA,yDAAA,CAA2D,GAC/D,CAAa,WAAA,CAAA,CAAA;OACpB;IACD,EAAA,qBAAqB,EAAEC,CAAC;IAAEC,IAAAA,IAAAA;IAAK,GAAC,KAAK;QACjC,OAAO,CAAA,qCAAA,EAAwCA,IAAI,CAAW,SAAA,CAAA,CAAA;OACjE;IACD,EAAA,sBAAsB,EAAEC,CAAC;IAAED,IAAAA,IAAAA;IAAK,GAAC,KAAK;IAClC,IAAA,OAAQ,CAAmBA,gBAAAA,EAAAA,IAAI,CAA2B,yBAAA,CAAA,GACtD,CAAmE,iEAAA,CAAA,CAAA;OAC1E;IACD,EAAA,8BAA8B,EAAEE,CAAC;QAAEC,UAAU;IAAEjC,IAAAA,SAAAA;IAAU,GAAC,KAAK;IAC3D,IAAA,OAAQ,QAAQiC,UAAU,CAAA,qCAAA,CAAuC,GAC7D,CAAA,CAAA,EAAIjC,SAAS,CAA+B,6BAAA,CAAA,CAAA;OACnD;IACD,EAAA,wBAAwB,EAAEkC,CAAC;QAAE3B,UAAU;QAAEC,SAAS;QAAEC,QAAQ;IAAET,IAAAA,SAAAA;IAAU,GAAC,KAAK;IAC1E,IAAA,OAAQ,CAAiBA,cAAAA,EAAAA,SAAS,CAAuC,qCAAA,CAAA,GACrE,CAA6BO,0BAAAA,EAAAA,UAAU,CAAIC,CAAAA,EAAAA,SAAS,CAAIC,CAAAA,EAAAA,QAAQ,CAAO,KAAA,CAAA,GACvE,CAAoB,kBAAA,CAAA,CAAA;OAC3B;IACD,EAAA,oBAAoB,EAAE0B,CAAC;QAAEjC,KAAK;QAAEkC,aAAa;QAAE7B,UAAU;QAAEC,SAAS;QAAEC,QAAQ;IAAET,IAAAA,SAAAA;IAAW,GAAC,KAAK;QAC7F,OAAQ,CAAA,cAAA,EAAiBA,SAAS,CAAkC,gCAAA,CAAA,GAChE,IAAIoC,aAAa,CAAA,qBAAA,EAAwBhC,IAAI,CAACC,SAAS,CAACH,KAAK,CAAC,CAAA,IAAA,CAAM,GACpE,CAAA,yBAAA,EAA4BK,UAAU,CAAA,CAAA,EAAIC,SAAS,CAAIC,CAAAA,EAAAA,QAAQ,CAAK,GAAA,CAAA,GACpE,CAAmB,iBAAA,CAAA,CAAA;OAC1B;IACD,EAAA,6BAA6B,EAAE4B,CAAC;QAAE9B,UAAU;QAAEC,SAAS;IAAEC,IAAAA,QAAAA;IAAS,GAAC,KAAK;QACpE,OAAQ,CAAA,gEAAA,CAAkE,GACtE,CAAMF,GAAAA,EAAAA,UAAU,IAAIC,SAAS,CAAA,CAAA,EAAIC,QAAQ,CAAE,CAAA,CAAA;OAClD;IACD,EAAA,8BAA8B,EAAE6B,CAAC;QAAE/B,UAAU;QAAEC,SAAS;IAAEC,IAAAA,QAAAA;IAAS,GAAC,KAAK;QACrE,OAAQ,CAAA,wDAAA,CAA0D,GAC9D,CAAMF,GAAAA,EAAAA,UAAU,IAAIC,SAAS,CAAA,CAAA,EAAIC,QAAQ,CAAE,CAAA,CAAA;OAClD;IACD,EAAA,gBAAgB,EAAE8B,CAAC;QAAEhC,UAAU;QAAEE,QAAQ;IAAET,IAAAA,SAAAA;IAAU,GAAC,KAAK;QACvD,IAAI,CAACA,SAAS,IAAI,CAACO,UAAU,IAAI,CAACE,QAAQ,EAAE;IACxC,MAAA,MAAM,IAAIN,KAAK,CAAC,CAAA,2CAAA,CAA6C,CAAC,CAAA;IAClE,KAAA;IACA,IAAA,OAAQ,CAA4BH,yBAAAA,EAAAA,SAAS,CAA8B,4BAAA,CAAA,GACvE,CAAsE,oEAAA,CAAA,GACtE,CAA2BO,wBAAAA,EAAAA,UAAU,CAAIE,CAAAA,EAAAA,QAAQ,CAAS,OAAA,CAAA,GAC1D,CAAY,UAAA,CAAA,CAAA;OACnB;MACD,uBAAuB,EAAE+B,MAAM;QAC3B,OAAQ,CAAA,8CAAA,CAAgD,GACpD,CAAgC,8BAAA,CAAA,CAAA;OACvC;MACD,iCAAiC,EAAEC,MAAM;QACrC,OAAQ,CAAA,0DAAA,CAA4D,GAChE,CAAkD,gDAAA,CAAA,CAAA;OACzD;MACD,2BAA2B,EAAEC,MAAM;QAC/B,OAAQ,CAAA,uDAAA,CAAyD,GAC7D,CAAoD,kDAAA,CAAA,CAAA;OAC3D;IACD,EAAA,oBAAoB,EAAEC,CAAC;IAAEC,IAAAA,qBAAAA;IAAsB,GAAC,KAAK;QACjD,IAAI,CAACA,qBAAqB,EAAE;IACxB,MAAA,MAAM,IAAIzC,KAAK,CAAC,CAAA,+CAAA,CAAiD,CAAC,CAAA;IACtE,KAAA;IACA,IAAA,OAAQ,CAAiE,+DAAA,CAAA,GACrE,CAAkCyC,+BAAAA,EAAAA,qBAAqB,CAAG,CAAA,CAAA,CAAA;OACjE;IACD,EAAA,mBAAmB,EAAEC,CAAC;IAAED,IAAAA,qBAAAA;IAAsB,GAAC,KAAK;QAChD,IAAI,CAACA,qBAAqB,EAAE;IACxB,MAAA,MAAM,IAAIzC,KAAK,CAAC,CAAA,8CAAA,CAAgD,CAAC,CAAA;IACrE,KAAA;IACA,IAAA,OAAQ,gEAAgE,GACpE,CAAA,6DAAA,CAA+D,GAC/D,CAAA,CAAA,EAAIyC,qBAAqB,CAAG,CAAA,CAAA,CAAA;OACnC;IACD,EAAA,sBAAsB,EAAEE,CAAC;IAAEF,IAAAA,qBAAAA;IAAsB,GAAC,KAAK;QACnD,IAAI,CAACA,qBAAqB,EAAE;IACxB,MAAA,MAAM,IAAIzC,KAAK,CAAC,CAAA,iDAAA,CAAmD,CAAC,CAAA;IACxE,KAAA;IACA,IAAA,OAAQ,kEAAkE,GACtE,CAAA,6DAAA,CAA+D,GAC/D,CAAA,CAAA,EAAIyC,qBAAqB,CAAG,CAAA,CAAA,CAAA;OACnC;MACD,iBAAiB,EAAEG,MAAM;IACrB,IAAA,OAAO,CAAoD,kDAAA,CAAA,CAAA;OAC9D;IACD,EAAA,uBAAuB,EAAEC,CAAC;QAAEC,IAAI;QAAEC,KAAK;IAAEC,IAAAA,GAAAA;IAAI,GAAC,KAAK;QAC/C,OAAQ,CAAA,WAAA,EAAcD,KAAK,CAAcC,WAAAA,EAAAA,GAAG,4BAA4B,GACpE,CAAA,iDAAA,EAAoDF,IAAI,CAAS,OAAA,CAAA,CAAA;OACxE;IACD,EAAA,kCAAkC,EAAEG,CAAC;QAAEC,GAAG;IAAErE,IAAAA,MAAAA;IAAO,GAAC,KAAK;IACrD,IAAA,OAAQ,oBAAoBqE,GAAG,CAAA,mBAAA,EAAsBrE,MAAM,CAAA,cAAA,CAAgB,GACvE,CAAoC,kCAAA,CAAA,CAAA;OAC3C;IACD,EAAA,4BAA4B,EAAEsE,CAAC;IAAED,IAAAA,GAAAA;IAAI,GAAC,KAAK;IACvC,IAAA,OAAQ,CAAkCA,+BAAAA,EAAAA,GAAG,CAA6B,2BAAA,CAAA,GACtE,CAAU,QAAA,CAAA,CAAA;OACjB;IACD,EAAA,aAAa,EAAEE,CAAC;QAAEF,GAAG;IAAEzE,IAAAA,KAAAA;IAAM,GAAC,KAAK;IAC/B,IAAA,IAAI4E,OAAO,GAAG,CAAmDH,gDAAAA,EAAAA,GAAG,CAAI,EAAA,CAAA,CAAA;IACxE,IAAA,IAAIzE,KAAK,EAAE;UACP4E,OAAO,IAAI,CAA4B5E,yBAAAA,EAAAA,KAAK,CAAG,CAAA,CAAA,CAAA;IACnD,KAAA;IACA,IAAA,OAAO4E,OAAO,CAAA;OACjB;IACD,EAAA,yBAAyB,EAAEC,CAAC;QAAEJ,GAAG;IAAEK,IAAAA,MAAAA;IAAO,GAAC,KAAK;QAC5C,OAAQ,CAAA,4BAAA,EAA+BL,GAAG,CAAA,QAAA,CAAU,IAC/CK,MAAM,GAAG,CAAA,wBAAA,EAA2BA,MAAM,CAAA,CAAA,CAAG,GAAG,CAAA,CAAA,CAAG,CAAC,CAAA;OAC5D;IACD,EAAA,mBAAmB,EAAEC,CAAC;IAAEN,IAAAA,GAAAA;IAAI,GAAC,KAAK;IAC9B,IAAA,OAAQ,CAA4BA,yBAAAA,EAAAA,GAAG,CAAiC,+BAAA,CAAA,GACpE,CAAgE,8DAAA,CAAA,CAAA;OACvE;IACD,EAAA,2CAA2C,EAAEO,CAAC;IAAEP,IAAAA,GAAAA;IAAI,GAAC,KAAK;IACtD,IAAA,OAAQ,+BAA+B,GACnC,CAAA,qEAAA,CAAuE,GACvE,CAAA,EAAGA,GAAG,CAA8D,4DAAA,CAAA,CAAA;OAC3E;IACD,EAAA,wBAAwB,EAAEQ,CAAC;QAAEC,SAAS;IAAET,IAAAA,GAAAA;IAAI,GAAC,KAAK;IAC9C,IAAA,OAAO,CAA0CS,uCAAAA,EAAAA,SAAS,CAAQT,KAAAA,EAAAA,GAAG,CAAG,CAAA,CAAA,CAAA;OAC3E;IACD,EAAA,4BAA4B,EAAEU,CAAC;IAAEC,IAAAA,MAAAA;IAAO,GAAC,KAAK;IAC1C,IAAA,OAAQ,CAAgE,8DAAA,CAAA,GACpE,CAAmDA,gDAAAA,EAAAA,MAAM,CAAG,CAAA,CAAA,CAAA;OACnE;IACD,EAAA,uBAAuB,EAAEC,CAAC;IAAEC,IAAAA,IAAAA;IAAK,GAAC,KAAK;IACnC,IAAA,MAAMV,OAAO,GAAG,CAAA,kDAAA,CAAoD,GAChE,CAAA,CAAA,EAAIU,IAAI,CAAa,WAAA,CAAA,CAAA;QACzB,IAAIA,IAAI,KAAK,gBAAgB,EAAE;IAC3B,MAAA,OAAQ,CAAGV,EAAAA,OAAO,CAAuD,qDAAA,CAAA,GACrE,CAA4B,0BAAA,CAAA,CAAA;IACpC,KAAA;QACA,OAAO,CAAA,EAAGA,OAAO,CAA+C,6CAAA,CAAA,CAAA;IACpE,GAAA;IACJ,CAAC;;ICnOD;IACA;AACA;IACA;IACA;IACA;IACA;IAUA,MAAMW,iBAAiB,GAAGA,CAACC,IAAI,EAAEC,OAAO,GAAG,EAAE,KAAK;IAC9C,EAAA,MAAMb,OAAO,GAAG1D,UAAQ,CAACsE,IAAI,CAAC,CAAA;MAC9B,IAAI,CAACZ,OAAO,EAAE;IACV,IAAA,MAAM,IAAIrD,KAAK,CAAC,CAAoCiE,iCAAAA,EAAAA,IAAI,IAAI,CAAC,CAAA;IACjE,GAAA;MACA,OAAOZ,OAAO,CAACa,OAAO,CAAC,CAAA;IAC3B,CAAC,CAAA;IACM,MAAMC,gBAAgB,GAAsDH,iBAAiB;;ICvBpG;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMI,YAAY,SAASpE,KAAK,CAAC;IAC7B;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACIqE,EAAAA,WAAWA,CAACC,SAAS,EAAEJ,OAAO,EAAE;IAC5B,IAAA,MAAMb,OAAO,GAAGc,gBAAgB,CAACG,SAAS,EAAEJ,OAAO,CAAC,CAAA;QACpD,KAAK,CAACb,OAAO,CAAC,CAAA;QACd,IAAI,CAAC1B,IAAI,GAAG2C,SAAS,CAAA;QACrB,IAAI,CAACJ,OAAO,GAAGA,OAAO,CAAA;IAC1B,GAAA;IACJ;;ICjCA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMK,OAAO,GAAGA,CAACxE,KAAK,EAAEmE,OAAO,KAAK;IAChC,EAAA,IAAI,CAACM,KAAK,CAACD,OAAO,CAACxE,KAAK,CAAC,EAAE;IACvB,IAAA,MAAM,IAAIqE,YAAY,CAAC,cAAc,EAAEF,OAAO,CAAC,CAAA;IACnD,GAAA;IACJ,CAAC,CAAA;IACD,MAAMO,SAAS,GAAGA,CAACC,MAAM,EAAE5D,cAAc,EAAEoD,OAAO,KAAK;IACnD,EAAA,MAAMH,IAAI,GAAG,OAAOW,MAAM,CAAC5D,cAAc,CAAC,CAAA;MAC1C,IAAIiD,IAAI,KAAK,UAAU,EAAE;IACrBG,IAAAA,OAAO,CAAC,gBAAgB,CAAC,GAAGpD,cAAc,CAAA;IAC1C,IAAA,MAAM,IAAIsD,YAAY,CAAC,kBAAkB,EAAEF,OAAO,CAAC,CAAA;IACvD,GAAA;IACJ,CAAC,CAAA;IACD,MAAMS,MAAM,GAAGA,CAACD,MAAM,EAAElE,YAAY,EAAE0D,OAAO,KAAK;IAC9C,EAAA,IAAI,OAAOQ,MAAM,KAAKlE,YAAY,EAAE;IAChC0D,IAAAA,OAAO,CAAC,cAAc,CAAC,GAAG1D,YAAY,CAAA;IACtC,IAAA,MAAM,IAAI4D,YAAY,CAAC,gBAAgB,EAAEF,OAAO,CAAC,CAAA;IACrD,GAAA;IACJ,CAAC,CAAA;IACD,MAAMU,UAAU,GAAGA,CAACF,MAAM;IAC1B;IACA;IACAzC,aAAa,EAAEiC,OAAO,KAAK;IACvB,EAAA,IAAI,EAAEQ,MAAM,YAAYzC,aAAa,CAAC,EAAE;IACpCiC,IAAAA,OAAO,CAAC,mBAAmB,CAAC,GAAGjC,aAAa,CAACN,IAAI,CAAA;IACjD,IAAA,MAAM,IAAIyC,YAAY,CAAC,iBAAiB,EAAEF,OAAO,CAAC,CAAA;IACtD,GAAA;IACJ,CAAC,CAAA;IACD,MAAMW,OAAO,GAAGA,CAAC9E,KAAK,EAAE+E,WAAW,EAAEZ,OAAO,KAAK;IAC7C,EAAA,IAAI,CAACY,WAAW,CAACC,QAAQ,CAAChF,KAAK,CAAC,EAAE;QAC9BmE,OAAO,CAAC,uBAAuB,CAAC,GAAG,CAAA,iBAAA,EAAoBjE,IAAI,CAACC,SAAS,CAAC4E,WAAW,CAAC,CAAG,CAAA,CAAA,CAAA;IACrF,IAAA,MAAM,IAAIV,YAAY,CAAC,eAAe,EAAEF,OAAO,CAAC,CAAA;IACpD,GAAA;IACJ,CAAC,CAAA;IACD,MAAMc,cAAc,GAAGA,CAACjF,KAAK;IAC7B;IACAkC,aAAa;IAAE;IACfiC,OAAO,KAAK;MACR,MAAMzF,KAAK,GAAG,IAAI2F,YAAY,CAAC,oBAAoB,EAAEF,OAAO,CAAC,CAAA;IAC7D,EAAA,IAAI,CAACM,KAAK,CAACD,OAAO,CAACxE,KAAK,CAAC,EAAE;IACvB,IAAA,MAAMtB,KAAK,CAAA;IACf,GAAA;IACA,EAAA,KAAK,MAAMwG,IAAI,IAAIlF,KAAK,EAAE;IACtB,IAAA,IAAI,EAAEkF,IAAI,YAAYhD,aAAa,CAAC,EAAE;IAClC,MAAA,MAAMxD,KAAK,CAAA;IACf,KAAA;IACJ,GAAA;IACJ,CAAC,CAAA;IACD,MAAMyG,kBAAkB,GAElB;MACET,SAAS;MACTF,OAAO;MACPK,UAAU;MACVC,OAAO;MACPF,MAAM;IACNK,EAAAA,cAAAA;IACJ,CAAC;;ICtEL;IACA,IAAI;IACAlH,EAAAA,IAAI,CAAC,uBAAuB,CAAC,IAAIC,CAAC,EAAE,CAAA;IACxC,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,MAAMmH,aAAa,GAAG,KAAK,CAAA;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACO,MAAMC,YAAY,GAAG,CACxB,QAAQ,EACR,KAAK,EACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,KAAK,CACR;;IC/BD;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACzC,EAAA,IAAIA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QACG;IACvCC,MAAAA,kBAAM,CAACd,SAAS,CAACa,OAAO,EAAE,QAAQ,EAAE;IAChClF,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,OAAO;IAClBC,QAAAA,QAAQ,EAAE,aAAa;IACvBT,QAAAA,SAAS,EAAE,SAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;IACA,IAAA,OAAOyF,OAAO,CAAA;IAClB,GAAC,MACI;QAC0C;IACvCC,MAAAA,kBAAM,CAACZ,MAAM,CAACW,OAAO,EAAE,UAAU,EAAE;IAC/BlF,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,OAAO;IAClBC,QAAAA,QAAQ,EAAE,aAAa;IACvBT,QAAAA,SAAS,EAAE,SAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,OAAO;IAAE2F,MAAAA,MAAM,EAAEF,OAAAA;SAAS,CAAA;IAC9B,GAAA;IACJ,CAAC;;ICvCD;IACA;AACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMG,KAAK,CAAC;IACR;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIpB,WAAWA,CAACqB,KAAK,EAAEJ,OAAO,EAAEzG,MAAM,GAAGsG,aAAa,EAAE;QACL;IACvCI,MAAAA,kBAAM,CAACZ,MAAM,CAACe,KAAK,EAAE,UAAU,EAAE;IAC7BtF,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,OAAO;IAClBC,QAAAA,QAAQ,EAAE,aAAa;IACvBT,QAAAA,SAAS,EAAE,OAAA;IACf,OAAC,CAAC,CAAA;IACF,MAAA,IAAIhB,MAAM,EAAE;IACR0G,QAAAA,kBAAM,CAACV,OAAO,CAAChG,MAAM,EAAEuG,YAAY,EAAE;IAAEvF,UAAAA,SAAS,EAAE,QAAA;IAAS,SAAC,CAAC,CAAA;IACjE,OAAA;IACJ,KAAA;IACA;IACA;IACA,IAAA,IAAI,CAACyF,OAAO,GAAGD,gBAAgB,CAACC,OAAO,CAAC,CAAA;QACxC,IAAI,CAACI,KAAK,GAAGA,KAAK,CAAA;QAClB,IAAI,CAAC7G,MAAM,GAAGA,MAAM,CAAA;IACxB,GAAA;IACA;IACJ;IACA;IACA;IACA;MACI8G,eAAeA,CAACL,OAAO,EAAE;IACrB,IAAA,IAAI,CAACM,YAAY,GAAGP,gBAAgB,CAACC,OAAO,CAAC,CAAA;IACjD,GAAA;IACJ;;IC1DA;IACA;AACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMO,WAAW,SAASJ,KAAK,CAAC;IAC5B;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIpB,EAAAA,WAAWA,CAACyB,MAAM,EAAER,OAAO,EAAEzG,MAAM,EAAE;QACU;IACvC0G,MAAAA,kBAAM,CAACX,UAAU,CAACkB,MAAM,EAAEC,MAAM,EAAE;IAC9B3F,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,aAAa;IACxBC,QAAAA,QAAQ,EAAE,aAAa;IACvBT,QAAAA,SAAS,EAAE,SAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,MAAM6F,KAAK,GAAGA,CAAC;IAAExC,MAAAA,GAAAA;IAAI,KAAC,KAAK;UACvB,MAAM8C,MAAM,GAAGF,MAAM,CAACG,IAAI,CAAC/C,GAAG,CAACgD,IAAI,CAAC,CAAA;IACpC;UACA,IAAI,CAACF,MAAM,EAAE;IACT,QAAA,OAAA;IACJ,OAAA;IACA;IACA;IACA;IACA;IACA,MAAA,IAAI9C,GAAG,CAACW,MAAM,KAAKsC,QAAQ,CAACtC,MAAM,IAAImC,MAAM,CAACI,KAAK,KAAK,CAAC,EAAE;YACX;cACvCnI,MAAM,CAACK,KAAK,CAAC,CAAA,wBAAA,EAA2BwH,MAAM,CAACO,QAAQ,EAAE,CAAA,yBAAA,CAA2B,GAChF,CAAiCnD,8BAAAA,EAAAA,GAAG,CAACmD,QAAQ,EAAE,CAA6B,2BAAA,CAAA,GAC5E,4DAA4D,CAAC,CAAA;IACrE,SAAA;IACA,QAAA,OAAA;IACJ,OAAA;IACA;IACA;IACA;IACA;IACA,MAAA,OAAOL,MAAM,CAACM,KAAK,CAAC,CAAC,CAAC,CAAA;SACzB,CAAA;IACD,IAAA,KAAK,CAACZ,KAAK,EAAEJ,OAAO,EAAEzG,MAAM,CAAC,CAAA;IACjC,GAAA;IACJ;;ICvEA;IACA;AACA;IACA;IACA;IACA;IACA;IAEA,MAAM0H,cAAc,GAAIrD,GAAG,IAAK;IAC5B,EAAA,MAAMsD,MAAM,GAAG,IAAIC,GAAG,CAACC,MAAM,CAACxD,GAAG,CAAC,EAAEiD,QAAQ,CAACD,IAAI,CAAC,CAAA;IAClD;IACA;IACA,EAAA,OAAOM,MAAM,CAACN,IAAI,CAACS,OAAO,CAAC,IAAIZ,MAAM,CAAC,CAAA,CAAA,EAAII,QAAQ,CAACtC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAA;IACrE,CAAC;;ICbD;IACA;AACA;IACA;IACA;IACA;IACA;IAQA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM+C,MAAM,CAAC;IACT;IACJ;IACA;IACIvC,EAAAA,WAAWA,GAAG;IACV,IAAA,IAAI,CAACwC,OAAO,GAAG,IAAIC,GAAG,EAAE,CAAA;IACxB,IAAA,IAAI,CAACC,kBAAkB,GAAG,IAAID,GAAG,EAAE,CAAA;IACvC,GAAA;IACA;IACJ;IACA;IACA;IACA;MACI,IAAIE,MAAMA,GAAG;QACT,OAAO,IAAI,CAACH,OAAO,CAAA;IACvB,GAAA;IACA;IACJ;IACA;IACA;IACII,EAAAA,gBAAgBA,GAAG;IACf;IACAnJ,IAAAA,IAAI,CAACoJ,gBAAgB,CAAC,OAAO,EAAIC,KAAK,IAAK;UACvC,MAAM;IAAEC,QAAAA,OAAAA;IAAQ,OAAC,GAAGD,KAAK,CAAA;IACzB,MAAA,MAAME,eAAe,GAAG,IAAI,CAACC,aAAa,CAAC;YAAEF,OAAO;IAAED,QAAAA,KAAAA;IAAM,OAAC,CAAC,CAAA;IAC9D,MAAA,IAAIE,eAAe,EAAE;IACjBF,QAAAA,KAAK,CAACI,WAAW,CAACF,eAAe,CAAC,CAAA;IACtC,OAAA;IACJ,KAAE,CAAC,CAAA;IACP,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIG,EAAAA,gBAAgBA,GAAG;IACf;IACA1J,IAAAA,IAAI,CAACoJ,gBAAgB,CAAC,SAAS,EAAIC,KAAK,IAAK;IACzC;IACA;UACA,IAAIA,KAAK,CAACM,IAAI,IAAIN,KAAK,CAACM,IAAI,CAAC1D,IAAI,KAAK,YAAY,EAAE;IAChD;YACA,MAAM;IAAE2D,UAAAA,OAAAA;aAAS,GAAGP,KAAK,CAACM,IAAI,CAAA;YACa;cACvCxJ,MAAM,CAACK,KAAK,CAAC,CAAA,4BAAA,CAA8B,EAAEoJ,OAAO,CAACC,WAAW,CAAC,CAAA;IACrE,SAAA;IACA,QAAA,MAAMC,eAAe,GAAGC,OAAO,CAACC,GAAG,CAACJ,OAAO,CAACC,WAAW,CAACI,GAAG,CAAE/G,KAAK,IAAK;IACnE,UAAA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;gBAC3BA,KAAK,GAAG,CAACA,KAAK,CAAC,CAAA;IACnB,WAAA;IACA,UAAA,MAAMoG,OAAO,GAAG,IAAIY,OAAO,CAAC,GAAGhH,KAAK,CAAC,CAAA;cACrC,OAAO,IAAI,CAACsG,aAAa,CAAC;gBAAEF,OAAO;IAAED,YAAAA,KAAAA;IAAM,WAAC,CAAC,CAAA;IAC7C;IACA;IACA;aACH,CAAC,CAAC,CAAC;IACJA,QAAAA,KAAK,CAACc,SAAS,CAACL,eAAe,CAAC,CAAA;IAChC;YACA,IAAIT,KAAK,CAACe,KAAK,IAAIf,KAAK,CAACe,KAAK,CAAC,CAAC,CAAC,EAAE;IAC/B,UAAA,KAAKN,eAAe,CAACO,IAAI,CAAC,MAAMhB,KAAK,CAACe,KAAK,CAAC,CAAC,CAAC,CAACE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;IACrE,SAAA;IACJ,OAAA;IACJ,KAAE,CAAC,CAAA;IACP,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACId,EAAAA,aAAaA,CAAC;QAAEF,OAAO;IAAED,IAAAA,KAAAA;IAAO,GAAC,EAAE;QACY;IACvC5B,MAAAA,kBAAM,CAACX,UAAU,CAACwC,OAAO,EAAEY,OAAO,EAAE;IAChC5H,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,QAAQ;IACnBC,QAAAA,QAAQ,EAAE,eAAe;IACzBT,QAAAA,SAAS,EAAE,iBAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;IACA,IAAA,MAAMqD,GAAG,GAAG,IAAIuD,GAAG,CAACW,OAAO,CAAClE,GAAG,EAAEiD,QAAQ,CAACD,IAAI,CAAC,CAAA;QAC/C,IAAI,CAAChD,GAAG,CAACmF,QAAQ,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;UACS;IACvCrK,QAAAA,MAAM,CAACK,KAAK,CAAC,CAAA,yDAAA,CAA2D,CAAC,CAAA;IAC7E,OAAA;IACA,MAAA,OAAA;IACJ,KAAA;QACA,MAAMiK,UAAU,GAAGrF,GAAG,CAACW,MAAM,KAAKsC,QAAQ,CAACtC,MAAM,CAAA;QACjD,MAAM;UAAE2E,MAAM;IAAEC,MAAAA,KAAAA;IAAM,KAAC,GAAG,IAAI,CAACC,iBAAiB,CAAC;UAC7CvB,KAAK;UACLC,OAAO;UACPmB,UAAU;IACVrF,MAAAA,GAAAA;IACJ,KAAC,CAAC,CAAA;IACF,IAAA,IAAIoC,OAAO,GAAGmD,KAAK,IAAIA,KAAK,CAACnD,OAAO,CAAA;QACpC,MAAMqD,aAAa,GAAG,EAAE,CAAA;QACmB;IACvC,MAAA,IAAIrD,OAAO,EAAE;YACTqD,aAAa,CAACC,IAAI,CAAC,CAAC,uCAAuC,EAAEH,KAAK,CAAC,CAAC,CAAA;IACpE,QAAA,IAAID,MAAM,EAAE;cACRG,aAAa,CAACC,IAAI,CAAC,CACf,sDAAsD,EACtDJ,MAAM,CACT,CAAC,CAAA;IACN,SAAA;IACJ,OAAA;IACJ,KAAA;IACA;IACA;IACA,IAAA,MAAM3J,MAAM,GAAGuI,OAAO,CAACvI,MAAM,CAAA;QAC7B,IAAI,CAACyG,OAAO,IAAI,IAAI,CAACyB,kBAAkB,CAAC8B,GAAG,CAAChK,MAAM,CAAC,EAAE;UACN;YACvC8J,aAAa,CAACC,IAAI,CAAC,CAAA,yCAAA,CAA2C,GAC1D,CAAmC/J,gCAAAA,EAAAA,MAAM,GAAG,CAAC,CAAA;IACrD,OAAA;UACAyG,OAAO,GAAG,IAAI,CAACyB,kBAAkB,CAAC+B,GAAG,CAACjK,MAAM,CAAC,CAAA;IACjD,KAAA;QACA,IAAI,CAACyG,OAAO,EAAE;UACiC;IACvC;IACA;YACArH,MAAM,CAACK,KAAK,CAAC,CAAA,oBAAA,EAAuBiI,cAAc,CAACrD,GAAG,CAAC,CAAA,CAAE,CAAC,CAAA;IAC9D,OAAA;IACA,MAAA,OAAA;IACJ,KAAA;QAC2C;IACvC;IACA;UACAjF,MAAM,CAACS,cAAc,CAAC,CAAA,yBAAA,EAA4B6H,cAAc,CAACrD,GAAG,CAAC,CAAA,CAAE,CAAC,CAAA;IACxEyF,MAAAA,aAAa,CAACI,OAAO,CAAEC,GAAG,IAAK;IAC3B,QAAA,IAAIxE,KAAK,CAACD,OAAO,CAACyE,GAAG,CAAC,EAAE;IACpB/K,UAAAA,MAAM,CAACM,GAAG,CAAC,GAAGyK,GAAG,CAAC,CAAA;IACtB,SAAC,MACI;IACD/K,UAAAA,MAAM,CAACM,GAAG,CAACyK,GAAG,CAAC,CAAA;IACnB,SAAA;IACJ,OAAC,CAAC,CAAA;UACF/K,MAAM,CAACU,QAAQ,EAAE,CAAA;IACrB,KAAA;IACA;IACA;IACA,IAAA,IAAI0I,eAAe,CAAA;QACnB,IAAI;IACAA,MAAAA,eAAe,GAAG/B,OAAO,CAACE,MAAM,CAAC;YAAEtC,GAAG;YAAEkE,OAAO;YAAED,KAAK;IAAEqB,QAAAA,MAAAA;IAAO,OAAC,CAAC,CAAA;SACpE,CACD,OAAOS,GAAG,EAAE;IACR5B,MAAAA,eAAe,GAAGQ,OAAO,CAACqB,MAAM,CAACD,GAAG,CAAC,CAAA;IACzC,KAAA;IACA;IACA,IAAA,MAAMrD,YAAY,GAAG6C,KAAK,IAAIA,KAAK,CAAC7C,YAAY,CAAA;QAChD,IAAIyB,eAAe,YAAYQ,OAAO,KACjC,IAAI,CAACsB,aAAa,IAAIvD,YAAY,CAAC,EAAE;IACtCyB,MAAAA,eAAe,GAAGA,eAAe,CAAC+B,KAAK,CAAC,MAAOH,GAAG,IAAK;IACnD;IACA,QAAA,IAAIrD,YAAY,EAAE;cAC6B;IACvC;IACA;gBACA3H,MAAM,CAACS,cAAc,CAAC,CAAmC,iCAAA,CAAA,GACrD,CAAI6H,CAAAA,EAAAA,cAAc,CAACrD,GAAG,CAAC,CAAA,wCAAA,CAA0C,CAAC,CAAA;IACtEjF,YAAAA,MAAM,CAACQ,KAAK,CAAC,CAAkB,gBAAA,CAAA,EAAEgK,KAAK,CAAC,CAAA;IACvCxK,YAAAA,MAAM,CAACQ,KAAK,CAACwK,GAAG,CAAC,CAAA;gBACjBhL,MAAM,CAACU,QAAQ,EAAE,CAAA;IACrB,WAAA;cACA,IAAI;IACA,YAAA,OAAO,MAAMiH,YAAY,CAACJ,MAAM,CAAC;kBAAEtC,GAAG;kBAAEkE,OAAO;kBAAED,KAAK;IAAEqB,cAAAA,MAAAA;IAAO,aAAC,CAAC,CAAA;eACpE,CACD,OAAOa,QAAQ,EAAE;gBACb,IAAIA,QAAQ,YAAYrJ,KAAK,EAAE;IAC3BiJ,cAAAA,GAAG,GAAGI,QAAQ,CAAA;IAClB,aAAA;IACJ,WAAA;IACJ,SAAA;YACA,IAAI,IAAI,CAACF,aAAa,EAAE;cACuB;IACvC;IACA;gBACAlL,MAAM,CAACS,cAAc,CAAC,CAAmC,iCAAA,CAAA,GACrD,CAAI6H,CAAAA,EAAAA,cAAc,CAACrD,GAAG,CAAC,CAAA,uCAAA,CAAyC,CAAC,CAAA;IACrEjF,YAAAA,MAAM,CAACQ,KAAK,CAAC,CAAkB,gBAAA,CAAA,EAAEgK,KAAK,CAAC,CAAA;IACvCxK,YAAAA,MAAM,CAACQ,KAAK,CAACwK,GAAG,CAAC,CAAA;gBACjBhL,MAAM,CAACU,QAAQ,EAAE,CAAA;IACrB,WAAA;IACA,UAAA,OAAO,IAAI,CAACwK,aAAa,CAAC3D,MAAM,CAAC;gBAAEtC,GAAG;gBAAEkE,OAAO;IAAED,YAAAA,KAAAA;IAAM,WAAC,CAAC,CAAA;IAC7D,SAAA;IACA,QAAA,MAAM8B,GAAG,CAAA;IACb,OAAC,CAAC,CAAA;IACN,KAAA;IACA,IAAA,OAAO5B,eAAe,CAAA;IAC1B,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIqB,EAAAA,iBAAiBA,CAAC;QAAExF,GAAG;QAAEqF,UAAU;QAAEnB,OAAO;IAAED,IAAAA,KAAAA;IAAO,GAAC,EAAE;IACpD,IAAA,MAAMH,MAAM,GAAG,IAAI,CAACH,OAAO,CAACiC,GAAG,CAAC1B,OAAO,CAACvI,MAAM,CAAC,IAAI,EAAE,CAAA;IACrD,IAAA,KAAK,MAAM4J,KAAK,IAAIzB,MAAM,EAAE;IACxB,MAAA,IAAIwB,MAAM,CAAA;IACV;IACA;IACA,MAAA,MAAMc,WAAW,GAAGb,KAAK,CAAC/C,KAAK,CAAC;YAAExC,GAAG;YAAEqF,UAAU;YAAEnB,OAAO;IAAED,QAAAA,KAAAA;IAAM,OAAC,CAAC,CAAA;IACpE,MAAA,IAAImC,WAAW,EAAE;YAC8B;IACvC;IACA;cACA,IAAIA,WAAW,YAAYzB,OAAO,EAAE;IAChC5J,YAAAA,MAAM,CAACO,IAAI,CAAC,CAAA,cAAA,EAAiB+H,cAAc,CAACrD,GAAG,CAAC,CAAA,WAAA,CAAa,GACzD,CAAsD,oDAAA,CAAA,GACtD,CAA8D,4DAAA,CAAA,EAAEuF,KAAK,CAAC,CAAA;IAC9E,WAAA;IACJ,SAAA;IACA;IACA;IACAD,QAAAA,MAAM,GAAGc,WAAW,CAAA;IACpB,QAAA,IAAI9E,KAAK,CAACD,OAAO,CAACiE,MAAM,CAAC,IAAIA,MAAM,CAACe,MAAM,KAAK,CAAC,EAAE;IAC9C;IACAf,UAAAA,MAAM,GAAGgB,SAAS,CAAA;IACtB,SAAC,MACI,IAAIF,WAAW,CAACjF,WAAW,KAAK7E,MAAM;IAAI;YAC3CA,MAAM,CAACC,IAAI,CAAC6J,WAAW,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;IACvC;IACAf,UAAAA,MAAM,GAAGgB,SAAS,CAAA;IACtB,SAAC,MACI,IAAI,OAAOF,WAAW,KAAK,SAAS,EAAE;IACvC;IACA;IACA;IACAd,UAAAA,MAAM,GAAGgB,SAAS,CAAA;IACtB,SAAA;IACA;YACA,OAAO;cAAEf,KAAK;IAAED,UAAAA,MAAAA;aAAQ,CAAA;IAC5B,OAAA;IACJ,KAAA;IACA;IACA,IAAA,OAAO,EAAE,CAAA;IACb,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIiB,EAAAA,iBAAiBA,CAACnE,OAAO,EAAEzG,MAAM,GAAGsG,aAAa,EAAE;QAC/C,IAAI,CAAC4B,kBAAkB,CAAC2C,GAAG,CAAC7K,MAAM,EAAEwG,gBAAgB,CAACC,OAAO,CAAC,CAAC,CAAA;IAClE,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;MACIK,eAAeA,CAACL,OAAO,EAAE;IACrB,IAAA,IAAI,CAAC6D,aAAa,GAAG9D,gBAAgB,CAACC,OAAO,CAAC,CAAA;IAClD,GAAA;IACA;IACJ;IACA;IACA;IACA;MACIqE,aAAaA,CAAClB,KAAK,EAAE;QAC0B;IACvClD,MAAAA,kBAAM,CAACZ,MAAM,CAAC8D,KAAK,EAAE,QAAQ,EAAE;IAC3BrI,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,QAAQ;IACnBC,QAAAA,QAAQ,EAAE,eAAe;IACzBT,QAAAA,SAAS,EAAE,OAAA;IACf,OAAC,CAAC,CAAA;IACF0F,MAAAA,kBAAM,CAACd,SAAS,CAACgE,KAAK,EAAE,OAAO,EAAE;IAC7BrI,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,QAAQ;IACnBC,QAAAA,QAAQ,EAAE,eAAe;IACzBT,QAAAA,SAAS,EAAE,OAAA;IACf,OAAC,CAAC,CAAA;UACF0F,kBAAM,CAACZ,MAAM,CAAC8D,KAAK,CAACnD,OAAO,EAAE,QAAQ,EAAE;IACnClF,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,QAAQ;IACnBC,QAAAA,QAAQ,EAAE,eAAe;IACzBT,QAAAA,SAAS,EAAE,OAAA;IACf,OAAC,CAAC,CAAA;UACF0F,kBAAM,CAACd,SAAS,CAACgE,KAAK,CAACnD,OAAO,EAAE,QAAQ,EAAE;IACtClF,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,QAAQ;IACnBC,QAAAA,QAAQ,EAAE,eAAe;IACzBT,QAAAA,SAAS,EAAE,eAAA;IACf,OAAC,CAAC,CAAA;UACF0F,kBAAM,CAACZ,MAAM,CAAC8D,KAAK,CAAC5J,MAAM,EAAE,QAAQ,EAAE;IAClCuB,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,QAAQ;IACnBC,QAAAA,QAAQ,EAAE,eAAe;IACzBT,QAAAA,SAAS,EAAE,cAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,IAAI,CAAC,IAAI,CAACgH,OAAO,CAACgC,GAAG,CAACJ,KAAK,CAAC5J,MAAM,CAAC,EAAE;UACjC,IAAI,CAACgI,OAAO,CAAC6C,GAAG,CAACjB,KAAK,CAAC5J,MAAM,EAAE,EAAE,CAAC,CAAA;IACtC,KAAA;IACA;IACA;IACA,IAAA,IAAI,CAACgI,OAAO,CAACiC,GAAG,CAACL,KAAK,CAAC5J,MAAM,CAAC,CAAC+J,IAAI,CAACH,KAAK,CAAC,CAAA;IAC9C,GAAA;IACA;IACJ;IACA;IACA;IACA;MACImB,eAAeA,CAACnB,KAAK,EAAE;QACnB,IAAI,CAAC,IAAI,CAAC5B,OAAO,CAACgC,GAAG,CAACJ,KAAK,CAAC5J,MAAM,CAAC,EAAE;IACjC,MAAA,MAAM,IAAIuF,YAAY,CAAC,4CAA4C,EAAE;YACjEvF,MAAM,EAAE4J,KAAK,CAAC5J,MAAAA;IAClB,OAAC,CAAC,CAAA;IACN,KAAA;IACA,IAAA,MAAMgL,UAAU,GAAG,IAAI,CAAChD,OAAO,CAACiC,GAAG,CAACL,KAAK,CAAC5J,MAAM,CAAC,CAACiL,OAAO,CAACrB,KAAK,CAAC,CAAA;IAChE,IAAA,IAAIoB,UAAU,GAAG,CAAC,CAAC,EAAE;IACjB,MAAA,IAAI,CAAChD,OAAO,CAACiC,GAAG,CAACL,KAAK,CAAC5J,MAAM,CAAC,CAACkL,MAAM,CAACF,UAAU,EAAE,CAAC,CAAC,CAAA;IACxD,KAAC,MACI;IACD,MAAA,MAAM,IAAIzF,YAAY,CAAC,uCAAuC,CAAC,CAAA;IACnE,KAAA;IACJ,GAAA;IACJ;;ICvYA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA,IAAI4F,aAAa,CAAA;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACO,MAAMC,wBAAwB,GAAGA,MAAM;MAC1C,IAAI,CAACD,aAAa,EAAE;IAChBA,IAAAA,aAAa,GAAG,IAAIpD,MAAM,EAAE,CAAA;IAC5B;QACAoD,aAAa,CAAC/C,gBAAgB,EAAE,CAAA;QAChC+C,aAAa,CAACxC,gBAAgB,EAAE,CAAA;IACpC,GAAA;IACA,EAAA,OAAOwC,aAAa,CAAA;IACxB,CAAC;;ICzBD;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASL,aAAaA,CAACO,OAAO,EAAE5E,OAAO,EAAEzG,MAAM,EAAE;IAC7C,EAAA,IAAI4J,KAAK,CAAA;IACT,EAAA,IAAI,OAAOyB,OAAO,KAAK,QAAQ,EAAE;QAC7B,MAAMC,UAAU,GAAG,IAAI1D,GAAG,CAACyD,OAAO,EAAE/D,QAAQ,CAACD,IAAI,CAAC,CAAA;QACP;IACvC,MAAA,IAAI,EAAEgE,OAAO,CAAC5B,UAAU,CAAC,GAAG,CAAC,IAAI4B,OAAO,CAAC5B,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE;IAC1D,QAAA,MAAM,IAAIlE,YAAY,CAAC,gBAAgB,EAAE;IACrChE,UAAAA,UAAU,EAAE,iBAAiB;IAC7BE,UAAAA,QAAQ,EAAE,eAAe;IACzBT,UAAAA,SAAS,EAAE,SAAA;IACf,SAAC,CAAC,CAAA;IACN,OAAA;IACA;IACA;IACA,MAAA,MAAMuK,YAAY,GAAGF,OAAO,CAAC5B,UAAU,CAAC,MAAM,CAAC,GACzC6B,UAAU,CAACE,QAAQ,GACnBH,OAAO,CAAA;IACb;UACA,MAAMI,SAAS,GAAG,QAAQ,CAAA;IAC1B,MAAA,IAAI,IAAIvE,MAAM,CAAC,CAAA,EAAGuE,SAAS,CAAA,CAAE,CAAC,CAACrE,IAAI,CAACmE,YAAY,CAAC,EAAE;YAC/CnM,MAAM,CAACK,KAAK,CAAC,CAA8D,4DAAA,CAAA,GACvE,cAAcgM,SAAS,CAAA,yCAAA,CAA2C,GAClE,CAAA,4DAAA,CAA8D,CAAC,CAAA;IACvE,OAAA;IACJ,KAAA;QACA,MAAMC,aAAa,GAAGA,CAAC;IAAErH,MAAAA,GAAAA;IAAI,KAAC,KAAK;UACY;IACvC,QAAA,IAAIA,GAAG,CAACmH,QAAQ,KAAKF,UAAU,CAACE,QAAQ,IACpCnH,GAAG,CAACW,MAAM,KAAKsG,UAAU,CAACtG,MAAM,EAAE;IAClC5F,UAAAA,MAAM,CAACK,KAAK,CAAC,CAAG4L,EAAAA,OAAO,+CAA+C,GAClE,CAAA,EAAGhH,GAAG,CAACmD,QAAQ,EAAE,CAAsD,oDAAA,CAAA,GACvE,+BAA+B,CAAC,CAAA;IACxC,SAAA;IACJ,OAAA;IACA,MAAA,OAAOnD,GAAG,CAACgD,IAAI,KAAKiE,UAAU,CAACjE,IAAI,CAAA;SACtC,CAAA;IACD;QACAuC,KAAK,GAAG,IAAIhD,KAAK,CAAC8E,aAAa,EAAEjF,OAAO,EAAEzG,MAAM,CAAC,CAAA;IACrD,GAAC,MACI,IAAIqL,OAAO,YAAYnE,MAAM,EAAE;IAChC;QACA0C,KAAK,GAAG,IAAI5C,WAAW,CAACqE,OAAO,EAAE5E,OAAO,EAAEzG,MAAM,CAAC,CAAA;IACrD,GAAC,MACI,IAAI,OAAOqL,OAAO,KAAK,UAAU,EAAE;IACpC;QACAzB,KAAK,GAAG,IAAIhD,KAAK,CAACyE,OAAO,EAAE5E,OAAO,EAAEzG,MAAM,CAAC,CAAA;IAC/C,GAAC,MACI,IAAIqL,OAAO,YAAYzE,KAAK,EAAE;IAC/BgD,IAAAA,KAAK,GAAGyB,OAAO,CAAA;IACnB,GAAC,MACI;IACD,IAAA,MAAM,IAAI9F,YAAY,CAAC,wBAAwB,EAAE;IAC7ChE,MAAAA,UAAU,EAAE,iBAAiB;IAC7BE,MAAAA,QAAQ,EAAE,eAAe;IACzBT,MAAAA,SAAS,EAAE,SAAA;IACf,KAAC,CAAC,CAAA;IACN,GAAA;IACA,EAAA,MAAMmK,aAAa,GAAGC,wBAAwB,EAAE,CAAA;IAChDD,EAAAA,aAAa,CAACL,aAAa,CAAClB,KAAK,CAAC,CAAA;IAClC,EAAA,OAAOA,KAAK,CAAA;IAChB;;IC3FA;IACA;AACA;IACA;IACA;IACA;IACA;IAEA,MAAM+B,iBAAiB,GAAG;IACtBC,EAAAA,eAAe,EAAE,iBAAiB;IAClCC,EAAAA,QAAQ,EAAE,aAAa;IACvBC,EAAAA,MAAM,EAAE,SAAS;IACjBC,EAAAA,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,OAAOC,YAAY,KAAK,WAAW,GAAGA,YAAY,CAACC,KAAK,GAAG,EAAA;IACvE,CAAC,CAAA;IACD,MAAMC,gBAAgB,GAAIrH,SAAS,IAAK;IACpC,EAAA,OAAO,CAAC6G,iBAAiB,CAACG,MAAM,EAAEhH,SAAS,EAAE6G,iBAAiB,CAACK,MAAM,CAAC,CACjEI,MAAM,CAAElL,KAAK,IAAKA,KAAK,IAAIA,KAAK,CAACwJ,MAAM,GAAG,CAAC,CAAC,CAC5ClK,IAAI,CAAC,GAAG,CAAC,CAAA;IAClB,CAAC,CAAA;IACD,MAAM6L,mBAAmB,GAAIC,EAAE,IAAK;MAChC,KAAK,MAAMzL,GAAG,IAAIF,MAAM,CAACC,IAAI,CAAC+K,iBAAiB,CAAC,EAAE;QAC9CW,EAAE,CAACzL,GAAG,CAAC,CAAA;IACX,GAAA;IACJ,CAAC,CAAA;IACM,MAAM0L,UAAU,GAAG;MACtBC,aAAa,EAAGnH,OAAO,IAAK;QACxBgH,mBAAmB,CAAExL,GAAG,IAAK;IACzB,MAAA,IAAI,OAAOwE,OAAO,CAACxE,GAAG,CAAC,KAAK,QAAQ,EAAE;IAClC8K,QAAAA,iBAAiB,CAAC9K,GAAG,CAAC,GAAGwE,OAAO,CAACxE,GAAG,CAAC,CAAA;IACzC,OAAA;IACJ,KAAC,CAAC,CAAA;OACL;MACD4L,sBAAsB,EAAGC,aAAa,IAAK;IACvC,IAAA,OAAOA,aAAa,IAAIP,gBAAgB,CAACR,iBAAiB,CAACC,eAAe,CAAC,CAAA;OAC9E;MACDe,eAAe,EAAGD,aAAa,IAAK;IAChC,IAAA,OAAOA,aAAa,IAAIP,gBAAgB,CAACR,iBAAiB,CAACE,QAAQ,CAAC,CAAA;OACvE;MACDe,SAAS,EAAEA,MAAM;QACb,OAAOjB,iBAAiB,CAACG,MAAM,CAAA;OAClC;MACDe,cAAc,EAAGH,aAAa,IAAK;IAC/B,IAAA,OAAOA,aAAa,IAAIP,gBAAgB,CAACR,iBAAiB,CAACI,OAAO,CAAC,CAAA;OACtE;MACDe,SAAS,EAAEA,MAAM;QACb,OAAOnB,iBAAiB,CAACK,MAAM,CAAA;IACnC,GAAA;IACJ,CAAC;;IChDD;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACO,SAASe,WAAWA,CAACC,OAAO,EAAE;IACjC;IACA,EAAA,KAAKA,OAAO,CAAC1D,IAAI,CAAC,MAAM,EAAG,CAAC,CAAA;IAChC;;ICfA;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA,MAAM2D,mBAAmB,GAAG,IAAIC,GAAG,EAAE;;ICXrC;IACA;AACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASC,0BAA0BA,CAACC,QAAQ,EAAE;MACC;IACvC1G,IAAAA,kBAAM,CAACZ,MAAM,CAACsH,QAAQ,EAAE,UAAU,EAAE;IAChC7L,MAAAA,UAAU,EAAE,cAAc;IAC1BE,MAAAA,QAAQ,EAAE,UAAU;IACpBT,MAAAA,SAAS,EAAE,UAAA;IACf,KAAC,CAAC,CAAA;IACN,GAAA;IACAiM,EAAAA,mBAAmB,CAACI,GAAG,CAACD,QAAQ,CAAC,CAAA;MACU;IACvChO,IAAAA,MAAM,CAACM,GAAG,CAAC,mDAAmD,EAAE0N,QAAQ,CAAC,CAAA;IAC7E,GAAA;IACJ;;;;;;;;;;;;IChCA,MAAME,aAAa,GAAGA,CAACzH,MAAM,EAAE0H,YAAY,KAAKA,YAAY,CAACC,IAAI,CAAEC,CAAC,IAAK5H,MAAM,YAAY4H,CAAC,CAAC,CAAA;IAE7F,IAAIC,iBAAiB,CAAA;IACrB,IAAIC,oBAAoB,CAAA;IACxB;IACA,SAASC,oBAAoBA,GAAG;IAC5B,EAAA,OAAQF,iBAAiB,KACpBA,iBAAiB,GAAG,CACjBG,WAAW,EACXC,cAAc,EACdC,QAAQ,EACRC,SAAS,EACTC,cAAc,CACjB,CAAC,CAAA;IACV,CAAA;IACA;IACA,SAASC,uBAAuBA,GAAG;MAC/B,OAAQP,oBAAoB,KACvBA,oBAAoB,GAAG,CACpBK,SAAS,CAACG,SAAS,CAACC,OAAO,EAC3BJ,SAAS,CAACG,SAAS,CAACE,QAAQ,EAC5BL,SAAS,CAACG,SAAS,CAACG,kBAAkB,CACzC,CAAC,CAAA;IACV,CAAA;IACA,MAAMC,gBAAgB,GAAG,IAAIC,OAAO,EAAE,CAAA;IACtC,MAAMC,kBAAkB,GAAG,IAAID,OAAO,EAAE,CAAA;IACxC,MAAME,wBAAwB,GAAG,IAAIF,OAAO,EAAE,CAAA;IAC9C,MAAMG,cAAc,GAAG,IAAIH,OAAO,EAAE,CAAA;IACpC,MAAMI,qBAAqB,GAAG,IAAIJ,OAAO,EAAE,CAAA;IAC3C,SAASK,gBAAgBA,CAACtG,OAAO,EAAE;MAC/B,MAAMyE,OAAO,GAAG,IAAIhE,OAAO,CAAC,CAAC8F,OAAO,EAAEzE,MAAM,KAAK;QAC7C,MAAM0E,QAAQ,GAAGA,MAAM;IACnBxG,MAAAA,OAAO,CAACyG,mBAAmB,CAAC,SAAS,EAAEC,OAAO,CAAC,CAAA;IAC/C1G,MAAAA,OAAO,CAACyG,mBAAmB,CAAC,OAAO,EAAEpP,KAAK,CAAC,CAAA;SAC9C,CAAA;QACD,MAAMqP,OAAO,GAAGA,MAAM;IAClBH,MAAAA,OAAO,CAACI,IAAI,CAAC3G,OAAO,CAACpB,MAAM,CAAC,CAAC,CAAA;IAC7B4H,MAAAA,QAAQ,EAAE,CAAA;SACb,CAAA;QACD,MAAMnP,KAAK,GAAGA,MAAM;IAChByK,MAAAA,MAAM,CAAC9B,OAAO,CAAC3I,KAAK,CAAC,CAAA;IACrBmP,MAAAA,QAAQ,EAAE,CAAA;SACb,CAAA;IACDxG,IAAAA,OAAO,CAACF,gBAAgB,CAAC,SAAS,EAAE4G,OAAO,CAAC,CAAA;IAC5C1G,IAAAA,OAAO,CAACF,gBAAgB,CAAC,OAAO,EAAEzI,KAAK,CAAC,CAAA;IAC5C,GAAC,CAAC,CAAA;IACFoN,EAAAA,OAAO,CACF1D,IAAI,CAAEpI,KAAK,IAAK;IACjB;IACA;QACA,IAAIA,KAAK,YAAY8M,SAAS,EAAE;IAC5BO,MAAAA,gBAAgB,CAAC1D,GAAG,CAAC3J,KAAK,EAAEqH,OAAO,CAAC,CAAA;IACxC,KAAA;IACA;IACJ,GAAC,CAAC,CACGgC,KAAK,CAAC,MAAM,EAAG,CAAC,CAAA;IACrB;IACA;IACAqE,EAAAA,qBAAqB,CAAC/D,GAAG,CAACmC,OAAO,EAAEzE,OAAO,CAAC,CAAA;IAC3C,EAAA,OAAOyE,OAAO,CAAA;IAClB,CAAA;IACA,SAASmC,8BAA8BA,CAACC,EAAE,EAAE;IACxC;IACA,EAAA,IAAIX,kBAAkB,CAACzE,GAAG,CAACoF,EAAE,CAAC,EAC1B,OAAA;MACJ,MAAMC,IAAI,GAAG,IAAIrG,OAAO,CAAC,CAAC8F,OAAO,EAAEzE,MAAM,KAAK;QAC1C,MAAM0E,QAAQ,GAAGA,MAAM;IACnBK,MAAAA,EAAE,CAACJ,mBAAmB,CAAC,UAAU,EAAEM,QAAQ,CAAC,CAAA;IAC5CF,MAAAA,EAAE,CAACJ,mBAAmB,CAAC,OAAO,EAAEpP,KAAK,CAAC,CAAA;IACtCwP,MAAAA,EAAE,CAACJ,mBAAmB,CAAC,OAAO,EAAEpP,KAAK,CAAC,CAAA;SACzC,CAAA;QACD,MAAM0P,QAAQ,GAAGA,MAAM;IACnBR,MAAAA,OAAO,EAAE,CAAA;IACTC,MAAAA,QAAQ,EAAE,CAAA;SACb,CAAA;QACD,MAAMnP,KAAK,GAAGA,MAAM;IAChByK,MAAAA,MAAM,CAAC+E,EAAE,CAACxP,KAAK,IAAI,IAAI2P,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC,CAAA;IAChER,MAAAA,QAAQ,EAAE,CAAA;SACb,CAAA;IACDK,IAAAA,EAAE,CAAC/G,gBAAgB,CAAC,UAAU,EAAEiH,QAAQ,CAAC,CAAA;IACzCF,IAAAA,EAAE,CAAC/G,gBAAgB,CAAC,OAAO,EAAEzI,KAAK,CAAC,CAAA;IACnCwP,IAAAA,EAAE,CAAC/G,gBAAgB,CAAC,OAAO,EAAEzI,KAAK,CAAC,CAAA;IACvC,GAAC,CAAC,CAAA;IACF;IACA6O,EAAAA,kBAAkB,CAAC5D,GAAG,CAACuE,EAAE,EAAEC,IAAI,CAAC,CAAA;IACpC,CAAA;IACA,IAAIG,aAAa,GAAG;IAChBvF,EAAAA,GAAGA,CAACwF,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAE;QACxB,IAAIF,MAAM,YAAYxB,cAAc,EAAE;IAClC;UACA,IAAIyB,IAAI,KAAK,MAAM,EACf,OAAOjB,kBAAkB,CAACxE,GAAG,CAACwF,MAAM,CAAC,CAAA;IACzC;UACA,IAAIC,IAAI,KAAK,kBAAkB,EAAE;YAC7B,OAAOD,MAAM,CAACG,gBAAgB,IAAIlB,wBAAwB,CAACzE,GAAG,CAACwF,MAAM,CAAC,CAAA;IAC1E,OAAA;IACA;UACA,IAAIC,IAAI,KAAK,OAAO,EAAE;IAClB,QAAA,OAAOC,QAAQ,CAACC,gBAAgB,CAAC,CAAC,CAAC,GAC7BjF,SAAS,GACTgF,QAAQ,CAACE,WAAW,CAACF,QAAQ,CAACC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAA;IAC5D,OAAA;IACJ,KAAA;IACA;IACA,IAAA,OAAOV,IAAI,CAACO,MAAM,CAACC,IAAI,CAAC,CAAC,CAAA;OAC5B;IACD7E,EAAAA,GAAGA,CAAC4E,MAAM,EAAEC,IAAI,EAAExO,KAAK,EAAE;IACrBuO,IAAAA,MAAM,CAACC,IAAI,CAAC,GAAGxO,KAAK,CAAA;IACpB,IAAA,OAAO,IAAI,CAAA;OACd;IACD8I,EAAAA,GAAGA,CAACyF,MAAM,EAAEC,IAAI,EAAE;IACd,IAAA,IAAID,MAAM,YAAYxB,cAAc,KAC/ByB,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,OAAO,CAAC,EAAE;IACvC,MAAA,OAAO,IAAI,CAAA;IACf,KAAA;QACA,OAAOA,IAAI,IAAID,MAAM,CAAA;IACzB,GAAA;IACJ,CAAC,CAAA;IACD,SAASK,YAAYA,CAAC1C,QAAQ,EAAE;IAC5BoC,EAAAA,aAAa,GAAGpC,QAAQ,CAACoC,aAAa,CAAC,CAAA;IAC3C,CAAA;IACA,SAASO,YAAYA,CAACC,IAAI,EAAE;IACxB;IACA;IACA;IACA,EAAA,IAAIA,IAAI,KAAKnC,WAAW,CAACM,SAAS,CAAC8B,WAAW,IAC1C,EAAE,kBAAkB,IAAIhC,cAAc,CAACE,SAAS,CAAC,EAAE;IACnD,IAAA,OAAO,UAAU+B,UAAU,EAAE,GAAGjQ,IAAI,EAAE;IAClC,MAAA,MAAMmP,EAAE,GAAGY,IAAI,CAACG,IAAI,CAACC,MAAM,CAAC,IAAI,CAAC,EAAEF,UAAU,EAAE,GAAGjQ,IAAI,CAAC,CAAA;IACvDyO,MAAAA,wBAAwB,CAAC7D,GAAG,CAACuE,EAAE,EAAEc,UAAU,CAACG,IAAI,GAAGH,UAAU,CAACG,IAAI,EAAE,GAAG,CAACH,UAAU,CAAC,CAAC,CAAA;UACpF,OAAOhB,IAAI,CAACE,EAAE,CAAC,CAAA;SAClB,CAAA;IACL,GAAA;IACA;IACA;IACA;IACA;IACA;MACA,IAAIlB,uBAAuB,EAAE,CAAChI,QAAQ,CAAC8J,IAAI,CAAC,EAAE;QAC1C,OAAO,UAAU,GAAG/P,IAAI,EAAE;IACtB;IACA;UACA+P,IAAI,CAACM,KAAK,CAACF,MAAM,CAAC,IAAI,CAAC,EAAEnQ,IAAI,CAAC,CAAA;UAC9B,OAAOiP,IAAI,CAACX,gBAAgB,CAACtE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;SAC1C,CAAA;IACL,GAAA;MACA,OAAO,UAAU,GAAGhK,IAAI,EAAE;IACtB;IACA;IACA,IAAA,OAAOiP,IAAI,CAACc,IAAI,CAACM,KAAK,CAACF,MAAM,CAAC,IAAI,CAAC,EAAEnQ,IAAI,CAAC,CAAC,CAAA;OAC9C,CAAA;IACL,CAAA;IACA,SAASsQ,sBAAsBA,CAACrP,KAAK,EAAE;MACnC,IAAI,OAAOA,KAAK,KAAK,UAAU,EAC3B,OAAO6O,YAAY,CAAC7O,KAAK,CAAC,CAAA;IAC9B;IACA;IACA,EAAA,IAAIA,KAAK,YAAY+M,cAAc,EAC/BkB,8BAA8B,CAACjO,KAAK,CAAC,CAAA;IACzC,EAAA,IAAIoM,aAAa,CAACpM,KAAK,EAAE0M,oBAAoB,EAAE,CAAC,EAC5C,OAAO,IAAI4C,KAAK,CAACtP,KAAK,EAAEsO,aAAa,CAAC,CAAA;IAC1C;IACA,EAAA,OAAOtO,KAAK,CAAA;IAChB,CAAA;IACA,SAASgO,IAAIA,CAAChO,KAAK,EAAE;IACjB;IACA;MACA,IAAIA,KAAK,YAAYuP,UAAU,EAC3B,OAAO5B,gBAAgB,CAAC3N,KAAK,CAAC,CAAA;IAClC;IACA;IACA,EAAA,IAAIyN,cAAc,CAAC3E,GAAG,CAAC9I,KAAK,CAAC,EACzB,OAAOyN,cAAc,CAAC1E,GAAG,CAAC/I,KAAK,CAAC,CAAA;IACpC,EAAA,MAAMwP,QAAQ,GAAGH,sBAAsB,CAACrP,KAAK,CAAC,CAAA;IAC9C;IACA;MACA,IAAIwP,QAAQ,KAAKxP,KAAK,EAAE;IACpByN,IAAAA,cAAc,CAAC9D,GAAG,CAAC3J,KAAK,EAAEwP,QAAQ,CAAC,CAAA;IACnC9B,IAAAA,qBAAqB,CAAC/D,GAAG,CAAC6F,QAAQ,EAAExP,KAAK,CAAC,CAAA;IAC9C,GAAA;IACA,EAAA,OAAOwP,QAAQ,CAAA;IACnB,CAAA;IACA,MAAMN,MAAM,GAAIlP,KAAK,IAAK0N,qBAAqB,CAAC3E,GAAG,CAAC/I,KAAK,CAAC;;ICnL1D;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASyP,MAAMA,CAAC7N,IAAI,EAAE8N,OAAO,EAAE;MAAEC,OAAO;MAAEC,OAAO;MAAEC,QAAQ;IAAEC,EAAAA,UAAAA;IAAW,CAAC,GAAG,EAAE,EAAE;MAC5E,MAAMzI,OAAO,GAAG0I,SAAS,CAACC,IAAI,CAACpO,IAAI,EAAE8N,OAAO,CAAC,CAAA;IAC7C,EAAA,MAAMO,WAAW,GAAGjC,IAAI,CAAC3G,OAAO,CAAC,CAAA;IACjC,EAAA,IAAIuI,OAAO,EAAE;IACTvI,IAAAA,OAAO,CAACF,gBAAgB,CAAC,eAAe,EAAGC,KAAK,IAAK;UACjDwI,OAAO,CAAC5B,IAAI,CAAC3G,OAAO,CAACpB,MAAM,CAAC,EAAEmB,KAAK,CAAC8I,UAAU,EAAE9I,KAAK,CAAC+I,UAAU,EAAEnC,IAAI,CAAC3G,OAAO,CAAC0H,WAAW,CAAC,EAAE3H,KAAK,CAAC,CAAA;IACvG,KAAC,CAAC,CAAA;IACN,GAAA;IACA,EAAA,IAAIuI,OAAO,EAAE;IACTtI,IAAAA,OAAO,CAACF,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAKuI,OAAO;IACtD;QACAvI,KAAK,CAAC8I,UAAU,EAAE9I,KAAK,CAAC+I,UAAU,EAAE/I,KAAK,CAAC,CAAC,CAAA;IAC/C,GAAA;IACA6I,EAAAA,WAAW,CACN7H,IAAI,CAAEgI,EAAE,IAAK;IACd,IAAA,IAAIN,UAAU,EACVM,EAAE,CAACjJ,gBAAgB,CAAC,OAAO,EAAE,MAAM2I,UAAU,EAAE,CAAC,CAAA;IACpD,IAAA,IAAID,QAAQ,EAAE;IACVO,MAAAA,EAAE,CAACjJ,gBAAgB,CAAC,eAAe,EAAGC,KAAK,IAAKyI,QAAQ,CAACzI,KAAK,CAAC8I,UAAU,EAAE9I,KAAK,CAAC+I,UAAU,EAAE/I,KAAK,CAAC,CAAC,CAAA;IACxG,KAAA;IACJ,GAAC,CAAC,CACGiC,KAAK,CAAC,MAAM,EAAG,CAAC,CAAA;IACrB,EAAA,OAAO4G,WAAW,CAAA;IACtB,CAAA;IACA;IACA;IACA;IACA;IACA;IACA,SAASI,QAAQA,CAACzO,IAAI,EAAE;IAAE+N,EAAAA,OAAAA;IAAQ,CAAC,GAAG,EAAE,EAAE;IACtC,EAAA,MAAMtI,OAAO,GAAG0I,SAAS,CAACO,cAAc,CAAC1O,IAAI,CAAC,CAAA;IAC9C,EAAA,IAAI+N,OAAO,EAAE;IACTtI,IAAAA,OAAO,CAACF,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAKuI,OAAO;IACtD;IACAvI,IAAAA,KAAK,CAAC8I,UAAU,EAAE9I,KAAK,CAAC,CAAC,CAAA;IAC7B,GAAA;MACA,OAAO4G,IAAI,CAAC3G,OAAO,CAAC,CAACe,IAAI,CAAC,MAAMqB,SAAS,CAAC,CAAA;IAC9C,CAAA;IAEA,MAAM8G,WAAW,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,CAAC,CAAA;IACtE,MAAMC,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;IACtD,MAAMC,aAAa,GAAG,IAAI1J,GAAG,EAAE,CAAA;IAC/B,SAAS2J,SAASA,CAACnC,MAAM,EAAEC,IAAI,EAAE;IAC7B,EAAA,IAAI,EAAED,MAAM,YAAY5B,WAAW,IAC/B,EAAE6B,IAAI,IAAID,MAAM,CAAC,IACjB,OAAOC,IAAI,KAAK,QAAQ,CAAC,EAAE;IAC3B,IAAA,OAAA;IACJ,GAAA;IACA,EAAA,IAAIiC,aAAa,CAAC1H,GAAG,CAACyF,IAAI,CAAC,EACvB,OAAOiC,aAAa,CAAC1H,GAAG,CAACyF,IAAI,CAAC,CAAA;MAClC,MAAMmC,cAAc,GAAGnC,IAAI,CAAC5H,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAA;IACrD,EAAA,MAAMgK,QAAQ,GAAGpC,IAAI,KAAKmC,cAAc,CAAA;IACxC,EAAA,MAAME,OAAO,GAAGL,YAAY,CAACxL,QAAQ,CAAC2L,cAAc,CAAC,CAAA;IACrD,EAAA;IACA;MACA,EAAEA,cAAc,IAAI,CAACC,QAAQ,GAAG/D,QAAQ,GAAGD,cAAc,EAAEK,SAAS,CAAC,IACjE,EAAE4D,OAAO,IAAIN,WAAW,CAACvL,QAAQ,CAAC2L,cAAc,CAAC,CAAC,EAAE;IACpD,IAAA,OAAA;IACJ,GAAA;MACA,MAAM7R,MAAM,GAAG,gBAAgBgS,SAAS,EAAE,GAAG/R,IAAI,EAAE;IAC/C;IACA,IAAA,MAAMmP,EAAE,GAAG,IAAI,CAACa,WAAW,CAAC+B,SAAS,EAAED,OAAO,GAAG,WAAW,GAAG,UAAU,CAAC,CAAA;IAC1E,IAAA,IAAItC,MAAM,GAAGL,EAAE,CAAC6C,KAAK,CAAA;IACrB,IAAA,IAAIH,QAAQ,EACRrC,MAAM,GAAGA,MAAM,CAAClI,KAAK,CAACtH,IAAI,CAACiS,KAAK,EAAE,CAAC,CAAA;IACvC;IACA;IACA;IACA;IACA;QACA,OAAO,CAAC,MAAMlJ,OAAO,CAACC,GAAG,CAAC,CACtBwG,MAAM,CAACoC,cAAc,CAAC,CAAC,GAAG5R,IAAI,CAAC,EAC/B8R,OAAO,IAAI3C,EAAE,CAACC,IAAI,CACrB,CAAC,EAAE,CAAC,CAAC,CAAA;OACT,CAAA;IACDsC,EAAAA,aAAa,CAAC9G,GAAG,CAAC6E,IAAI,EAAE1P,MAAM,CAAC,CAAA;IAC/B,EAAA,OAAOA,MAAM,CAAA;IACjB,CAAA;IACA8P,YAAY,CAAEqC,QAAQ,IAAAC,QAAA,KACfD,QAAQ,EAAA;MACXlI,GAAG,EAAEA,CAACwF,MAAM,EAAEC,IAAI,EAAEC,QAAQ,KAAKiC,SAAS,CAACnC,MAAM,EAAEC,IAAI,CAAC,IAAIyC,QAAQ,CAAClI,GAAG,CAACwF,MAAM,EAAEC,IAAI,EAAEC,QAAQ,CAAC;MAChG3F,GAAG,EAAEA,CAACyF,MAAM,EAAEC,IAAI,KAAK,CAAC,CAACkC,SAAS,CAACnC,MAAM,EAAEC,IAAI,CAAC,IAAIyC,QAAQ,CAACnI,GAAG,CAACyF,MAAM,EAAEC,IAAI,CAAA;IAAC,CAAA,CAChF,CAAC;;IC3FH;IACA,IAAI;IACAzQ,EAAAA,IAAI,CAAC,0BAA0B,CAAC,IAAIC,CAAC,EAAE,CAAA;IAC3C,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAGA,MAAMkT,OAAO,GAAG,oBAAoB,CAAA;IACpC,MAAMC,kBAAkB,GAAG,eAAe,CAAA;IAC1C,MAAMC,YAAY,GAAIC,eAAe,IAAK;MACtC,MAAMnO,GAAG,GAAG,IAAIuD,GAAG,CAAC4K,eAAe,EAAElL,QAAQ,CAACD,IAAI,CAAC,CAAA;MACnDhD,GAAG,CAACoO,IAAI,GAAG,EAAE,CAAA;MACb,OAAOpO,GAAG,CAACgD,IAAI,CAAA;IACnB,CAAC,CAAA;IACD;IACA;IACA;IACA;IACA;IACA,MAAMqL,oBAAoB,CAAC;IACvB;IACJ;IACA;IACA;IACA;IACA;MACIlN,WAAWA,CAACV,SAAS,EAAE;QACnB,IAAI,CAAC6N,GAAG,GAAG,IAAI,CAAA;QACf,IAAI,CAACC,UAAU,GAAG9N,SAAS,CAAA;IAC/B,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;MACI+N,UAAUA,CAACvB,EAAE,EAAE;IACX;IACA;IACA;IACA;IACA,IAAA,MAAMwB,QAAQ,GAAGxB,EAAE,CAACyB,iBAAiB,CAACT,kBAAkB,EAAE;IAAEU,MAAAA,OAAO,EAAE,IAAA;IAAK,KAAC,CAAC,CAAA;IAC5E;IACA;IACA;IACAF,IAAAA,QAAQ,CAACG,WAAW,CAAC,WAAW,EAAE,WAAW,EAAE;IAAEC,MAAAA,MAAM,EAAE,KAAA;IAAM,KAAC,CAAC,CAAA;IACjEJ,IAAAA,QAAQ,CAACG,WAAW,CAAC,WAAW,EAAE,WAAW,EAAE;IAAEC,MAAAA,MAAM,EAAE,KAAA;IAAM,KAAC,CAAC,CAAA;IACrE,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;MACIC,yBAAyBA,CAAC7B,EAAE,EAAE;IAC1B,IAAA,IAAI,CAACuB,UAAU,CAACvB,EAAE,CAAC,CAAA;QACnB,IAAI,IAAI,CAACsB,UAAU,EAAE;IACjB,MAAA,KAAKrB,QAAQ,CAAC,IAAI,CAACqB,UAAU,CAAC,CAAA;IAClC,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMQ,YAAYA,CAAC/O,GAAG,EAAEgP,SAAS,EAAE;IAC/BhP,IAAAA,GAAG,GAAGkO,YAAY,CAAClO,GAAG,CAAC,CAAA;IACvB,IAAA,MAAMlC,KAAK,GAAG;UACVkC,GAAG;UACHgP,SAAS;UACTvO,SAAS,EAAE,IAAI,CAAC8N,UAAU;IAC1B;IACA;IACA;IACAU,MAAAA,EAAE,EAAE,IAAI,CAACC,MAAM,CAAClP,GAAG,CAAA;SACtB,CAAA;IACD,IAAA,MAAMiN,EAAE,GAAG,MAAM,IAAI,CAACkC,KAAK,EAAE,CAAA;QAC7B,MAAMpE,EAAE,GAAGkC,EAAE,CAACrB,WAAW,CAACqC,kBAAkB,EAAE,WAAW,EAAE;IACvDmB,MAAAA,UAAU,EAAE,SAAA;IAChB,KAAC,CAAC,CAAA;IACF,IAAA,MAAMrE,EAAE,CAAC6C,KAAK,CAACyB,GAAG,CAACvR,KAAK,CAAC,CAAA;QACzB,MAAMiN,EAAE,CAACC,IAAI,CAAA;IACjB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;MACI,MAAMsE,YAAYA,CAACtP,GAAG,EAAE;IACpB,IAAA,MAAMiN,EAAE,GAAG,MAAM,IAAI,CAACkC,KAAK,EAAE,CAAA;IAC7B,IAAA,MAAMrR,KAAK,GAAG,MAAMmP,EAAE,CAACrH,GAAG,CAACqI,kBAAkB,EAAE,IAAI,CAACiB,MAAM,CAAClP,GAAG,CAAC,CAAC,CAAA;IAChE,IAAA,OAAOlC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACkR,SAAS,CAAA;IACxE,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMO,aAAaA,CAACC,YAAY,EAAEC,QAAQ,EAAE;IACxC,IAAA,MAAMxC,EAAE,GAAG,MAAM,IAAI,CAACkC,KAAK,EAAE,CAAA;QAC7B,IAAIO,MAAM,GAAG,MAAMzC,EAAE,CAChBrB,WAAW,CAACqC,kBAAkB,CAAC,CAC/BL,KAAK,CAAC1K,KAAK,CAAC,WAAW,CAAC,CACxByM,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QAC7B,MAAMC,eAAe,GAAG,EAAE,CAAA;QAC1B,IAAIC,sBAAsB,GAAG,CAAC,CAAA;IAC9B,IAAA,OAAOH,MAAM,EAAE;IACX,MAAA,MAAM5M,MAAM,GAAG4M,MAAM,CAAC7S,KAAK,CAAA;IAC3B;IACA;IACA,MAAA,IAAIiG,MAAM,CAACrC,SAAS,KAAK,IAAI,CAAC8N,UAAU,EAAE;IACtC;IACA;IACA,QAAA,IAAKiB,YAAY,IAAI1M,MAAM,CAACkM,SAAS,GAAGQ,YAAY,IAC/CC,QAAQ,IAAII,sBAAsB,IAAIJ,QAAS,EAAE;IAClD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAG,UAAAA,eAAe,CAAClK,IAAI,CAACgK,MAAM,CAAC7S,KAAK,CAAC,CAAA;IACtC,SAAC,MACI;IACDgT,UAAAA,sBAAsB,EAAE,CAAA;IAC5B,SAAA;IACJ,OAAA;IACAH,MAAAA,MAAM,GAAG,MAAMA,MAAM,CAAC1F,QAAQ,EAAE,CAAA;IACpC,KAAA;IACA;IACA;IACA;IACA;QACA,MAAM8F,WAAW,GAAG,EAAE,CAAA;IACtB,IAAA,KAAK,MAAMhS,KAAK,IAAI8R,eAAe,EAAE;UACjC,MAAM3C,EAAE,CAAC8C,MAAM,CAAC9B,kBAAkB,EAAEnQ,KAAK,CAACmR,EAAE,CAAC,CAAA;IAC7Ca,MAAAA,WAAW,CAACpK,IAAI,CAAC5H,KAAK,CAACkC,GAAG,CAAC,CAAA;IAC/B,KAAA;IACA,IAAA,OAAO8P,WAAW,CAAA;IACtB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;MACIZ,MAAMA,CAAClP,GAAG,EAAE;IACR;IACA;IACA;QACA,OAAO,IAAI,CAACuO,UAAU,GAAG,GAAG,GAAGL,YAAY,CAAClO,GAAG,CAAC,CAAA;IACpD,GAAA;IACA;IACJ;IACA;IACA;IACA;MACI,MAAMmP,KAAKA,GAAG;IACV,IAAA,IAAI,CAAC,IAAI,CAACb,GAAG,EAAE;UACX,IAAI,CAACA,GAAG,GAAG,MAAMhC,MAAM,CAAC0B,OAAO,EAAE,CAAC,EAAE;IAChCvB,QAAAA,OAAO,EAAE,IAAI,CAACqC,yBAAyB,CAACkB,IAAI,CAAC,IAAI,CAAA;IACrD,OAAC,CAAC,CAAA;IACN,KAAA;QACA,OAAO,IAAI,CAAC1B,GAAG,CAAA;IACnB,GAAA;IACJ;;ICvLA;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM2B,eAAe,CAAC;IAClB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI9O,EAAAA,WAAWA,CAACV,SAAS,EAAEyP,MAAM,GAAG,EAAE,EAAE;QAChC,IAAI,CAACC,UAAU,GAAG,KAAK,CAAA;QACvB,IAAI,CAACC,eAAe,GAAG,KAAK,CAAA;QACe;IACvC/N,MAAAA,kBAAM,CAACZ,MAAM,CAAChB,SAAS,EAAE,QAAQ,EAAE;IAC/BvD,QAAAA,UAAU,EAAE,oBAAoB;IAChCC,QAAAA,SAAS,EAAE,iBAAiB;IAC5BC,QAAAA,QAAQ,EAAE,aAAa;IACvBT,QAAAA,SAAS,EAAE,WAAA;IACf,OAAC,CAAC,CAAA;UACF,IAAI,EAAEuT,MAAM,CAACG,UAAU,IAAIH,MAAM,CAACI,aAAa,CAAC,EAAE;IAC9C,QAAA,MAAM,IAAIpP,YAAY,CAAC,6BAA6B,EAAE;IAClDhE,UAAAA,UAAU,EAAE,oBAAoB;IAChCC,UAAAA,SAAS,EAAE,iBAAiB;IAC5BC,UAAAA,QAAQ,EAAE,aAAA;IACd,SAAC,CAAC,CAAA;IACN,OAAA;UACA,IAAI8S,MAAM,CAACG,UAAU,EAAE;YACnBhO,kBAAM,CAACZ,MAAM,CAACyO,MAAM,CAACG,UAAU,EAAE,QAAQ,EAAE;IACvCnT,UAAAA,UAAU,EAAE,oBAAoB;IAChCC,UAAAA,SAAS,EAAE,iBAAiB;IAC5BC,UAAAA,QAAQ,EAAE,aAAa;IACvBT,UAAAA,SAAS,EAAE,mBAAA;IACf,SAAC,CAAC,CAAA;IACN,OAAA;UACA,IAAIuT,MAAM,CAACI,aAAa,EAAE;YACtBjO,kBAAM,CAACZ,MAAM,CAACyO,MAAM,CAACI,aAAa,EAAE,QAAQ,EAAE;IAC1CpT,UAAAA,UAAU,EAAE,oBAAoB;IAChCC,UAAAA,SAAS,EAAE,iBAAiB;IAC5BC,UAAAA,QAAQ,EAAE,aAAa;IACvBT,UAAAA,SAAS,EAAE,sBAAA;IACf,SAAC,CAAC,CAAA;IACN,OAAA;IACJ,KAAA;IACA,IAAA,IAAI,CAAC4T,WAAW,GAAGL,MAAM,CAACG,UAAU,CAAA;IACpC,IAAA,IAAI,CAACG,cAAc,GAAGN,MAAM,CAACI,aAAa,CAAA;IAC1C,IAAA,IAAI,CAACG,aAAa,GAAGP,MAAM,CAACQ,YAAY,CAAA;QACxC,IAAI,CAACnC,UAAU,GAAG9N,SAAS,CAAA;IAC3B,IAAA,IAAI,CAACkQ,eAAe,GAAG,IAAItC,oBAAoB,CAAC5N,SAAS,CAAC,CAAA;IAC9D,GAAA;IACA;IACJ;IACA;MACI,MAAM8O,aAAaA,GAAG;QAClB,IAAI,IAAI,CAACY,UAAU,EAAE;UACjB,IAAI,CAACC,eAAe,GAAG,IAAI,CAAA;IAC3B,MAAA,OAAA;IACJ,KAAA;QACA,IAAI,CAACD,UAAU,GAAG,IAAI,CAAA;IACtB,IAAA,MAAMX,YAAY,GAAG,IAAI,CAACgB,cAAc,GAClCI,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACL,cAAc,GAAG,IAAI,GACvC,CAAC,CAAA;IACP,IAAA,MAAMM,WAAW,GAAG,MAAM,IAAI,CAACH,eAAe,CAACpB,aAAa,CAACC,YAAY,EAAE,IAAI,CAACe,WAAW,CAAC,CAAA;IAC5F;IACA,IAAA,MAAMQ,KAAK,GAAG,MAAMnW,IAAI,CAACoW,MAAM,CAACnE,IAAI,CAAC,IAAI,CAAC0B,UAAU,CAAC,CAAA;IACrD,IAAA,KAAK,MAAMvO,GAAG,IAAI8Q,WAAW,EAAE;UAC3B,MAAMC,KAAK,CAAChB,MAAM,CAAC/P,GAAG,EAAE,IAAI,CAACyQ,aAAa,CAAC,CAAA;IAC/C,KAAA;QAC2C;IACvC,MAAA,IAAIK,WAAW,CAACzK,MAAM,GAAG,CAAC,EAAE;IACxBtL,QAAAA,MAAM,CAACS,cAAc,CAAC,CAAWsV,QAAAA,EAAAA,WAAW,CAACzK,MAAM,CAAA,CAAA,CAAG,GAClD,CAAA,EAAGyK,WAAW,CAACzK,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS,CAAe,aAAA,CAAA,GAChE,GAAGyK,WAAW,CAACzK,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,MAAM,YAAY,GACvD,CAAA,CAAA,EAAI,IAAI,CAACkI,UAAU,UAAU,CAAC,CAAA;IAClCxT,QAAAA,MAAM,CAACM,GAAG,CAAC,CAAA,sBAAA,EAAyByV,WAAW,CAACzK,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,GAAG,CAAC,CAAA;IACjFyK,QAAAA,WAAW,CAACjL,OAAO,CAAE7F,GAAG,IAAKjF,MAAM,CAACM,GAAG,CAAC,CAAA,IAAA,EAAO2E,GAAG,CAAA,CAAE,CAAC,CAAC,CAAA;YACtDjF,MAAM,CAACU,QAAQ,EAAE,CAAA;IACrB,OAAC,MACI;IACDV,QAAAA,MAAM,CAACK,KAAK,CAAC,CAAA,oDAAA,CAAsD,CAAC,CAAA;IACxE,OAAA;IACJ,KAAA;QACA,IAAI,CAAC+U,UAAU,GAAG,KAAK,CAAA;QACvB,IAAI,IAAI,CAACC,eAAe,EAAE;UACtB,IAAI,CAACA,eAAe,GAAG,KAAK,CAAA;IAC5B1H,MAAAA,WAAW,CAAC,IAAI,CAAC6G,aAAa,EAAE,CAAC,CAAA;IACrC,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;MACI,MAAM0B,eAAeA,CAACjR,GAAG,EAAE;QACoB;IACvCqC,MAAAA,kBAAM,CAACZ,MAAM,CAACzB,GAAG,EAAE,QAAQ,EAAE;IACzB9C,QAAAA,UAAU,EAAE,oBAAoB;IAChCC,QAAAA,SAAS,EAAE,iBAAiB;IAC5BC,QAAAA,QAAQ,EAAE,iBAAiB;IAC3BT,QAAAA,SAAS,EAAE,KAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;IACA,IAAA,MAAM,IAAI,CAACgU,eAAe,CAAC5B,YAAY,CAAC/O,GAAG,EAAE4Q,IAAI,CAACC,GAAG,EAAE,CAAC,CAAA;IAC5D,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI,MAAMK,YAAYA,CAAClR,GAAG,EAAE;IACpB,IAAA,IAAI,CAAC,IAAI,CAACwQ,cAAc,EAAE;UACqB;IACvC,QAAA,MAAM,IAAItP,YAAY,CAAC,CAAA,4BAAA,CAA8B,EAAE;IACnDtC,UAAAA,UAAU,EAAE,cAAc;IAC1BjC,UAAAA,SAAS,EAAE,eAAA;IACf,SAAC,CAAC,CAAA;IACN,OAAA;IAEJ,KAAC,MACI;UACD,MAAMqS,SAAS,GAAG,MAAM,IAAI,CAAC2B,eAAe,CAACrB,YAAY,CAACtP,GAAG,CAAC,CAAA;IAC9D,MAAA,MAAMmR,eAAe,GAAGP,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACL,cAAc,GAAG,IAAI,CAAA;UAC/D,OAAOxB,SAAS,KAAK1I,SAAS,GAAG0I,SAAS,GAAGmC,eAAe,GAAG,IAAI,CAAA;IACvE,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;MACI,MAAMpB,MAAMA,GAAG;IACX;IACA;QACA,IAAI,CAACK,eAAe,GAAG,KAAK,CAAA;QAC5B,MAAM,IAAI,CAACO,eAAe,CAACpB,aAAa,CAAC6B,QAAQ,CAAC,CAAC;IACvD,GAAA;IACJ;;ICvKA;IACA;AACA;IACA;IACA;IACA;IACA;IAUA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,gBAAgB,CAAC;IACnB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIlQ,EAAAA,WAAWA,CAAC+O,MAAM,GAAG,EAAE,EAAE;IACrB;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;QACQ,IAAI,CAACoB,wBAAwB,GAAG,OAAO;UAAErN,KAAK;UAAEC,OAAO;UAAEzD,SAAS;IAAE8Q,MAAAA,cAAAA;IAAgB,KAAC,KAAK;UACtF,IAAI,CAACA,cAAc,EAAE;IACjB,QAAA,OAAO,IAAI,CAAA;IACf,OAAA;IACA,MAAA,MAAMC,OAAO,GAAG,IAAI,CAACC,oBAAoB,CAACF,cAAc,CAAC,CAAA;IACzD;IACA;IACA,MAAA,MAAMG,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAAClR,SAAS,CAAC,CAAA;IAC3DiI,MAAAA,WAAW,CAACgJ,eAAe,CAACnC,aAAa,EAAE,CAAC,CAAA;IAC5C;IACA;UACA,MAAMqC,mBAAmB,GAAGF,eAAe,CAACT,eAAe,CAAC/M,OAAO,CAAClE,GAAG,CAAC,CAAA;IACxE,MAAA,IAAIiE,KAAK,EAAE;YACP,IAAI;IACAA,UAAAA,KAAK,CAACc,SAAS,CAAC6M,mBAAmB,CAAC,CAAA;aACvC,CACD,OAAOrW,KAAK,EAAE;cACiC;IACvC;gBACA,IAAI,SAAS,IAAI0I,KAAK,EAAE;IACpBlJ,cAAAA,MAAM,CAACO,IAAI,CAAC,CAAmD,iDAAA,CAAA,GAC3D,2BAA2B,GAC3B,CAAA,CAAA,EAAI+H,cAAc,CAACY,KAAK,CAACC,OAAO,CAAClE,GAAG,CAAC,IAAI,CAAC,CAAA;IAClD,aAAA;IACJ,WAAA;IACJ,SAAA;IACJ,OAAA;IACA,MAAA,OAAOwR,OAAO,GAAGD,cAAc,GAAG,IAAI,CAAA;SACzC,CAAA;IACD;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;QACQ,IAAI,CAACM,cAAc,GAAG,OAAO;UAAEpR,SAAS;IAAEyD,MAAAA,OAAAA;IAAS,KAAC,KAAK;UACV;IACvC7B,QAAAA,kBAAM,CAACZ,MAAM,CAAChB,SAAS,EAAE,QAAQ,EAAE;IAC/BvD,UAAAA,UAAU,EAAE,oBAAoB;IAChCC,UAAAA,SAAS,EAAE,QAAQ;IACnBC,UAAAA,QAAQ,EAAE,gBAAgB;IAC1BT,UAAAA,SAAS,EAAE,WAAA;IACf,SAAC,CAAC,CAAA;IACF0F,QAAAA,kBAAM,CAACX,UAAU,CAACwC,OAAO,EAAEY,OAAO,EAAE;IAChC5H,UAAAA,UAAU,EAAE,oBAAoB;IAChCC,UAAAA,SAAS,EAAE,QAAQ;IACnBC,UAAAA,QAAQ,EAAE,gBAAgB;IAC1BT,UAAAA,SAAS,EAAE,SAAA;IACf,SAAC,CAAC,CAAA;IACN,OAAA;IACA,MAAA,MAAM+U,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAAClR,SAAS,CAAC,CAAA;IAC3D,MAAA,MAAMiR,eAAe,CAACT,eAAe,CAAC/M,OAAO,CAAClE,GAAG,CAAC,CAAA;IAClD,MAAA,MAAM0R,eAAe,CAACnC,aAAa,EAAE,CAAA;SACxC,CAAA;QAC0C;UACvC,IAAI,EAAEW,MAAM,CAACG,UAAU,IAAIH,MAAM,CAACI,aAAa,CAAC,EAAE;IAC9C,QAAA,MAAM,IAAIpP,YAAY,CAAC,6BAA6B,EAAE;IAClDhE,UAAAA,UAAU,EAAE,oBAAoB;IAChCC,UAAAA,SAAS,EAAE,QAAQ;IACnBC,UAAAA,QAAQ,EAAE,aAAA;IACd,SAAC,CAAC,CAAA;IACN,OAAA;UACA,IAAI8S,MAAM,CAACG,UAAU,EAAE;YACnBhO,kBAAM,CAACZ,MAAM,CAACyO,MAAM,CAACG,UAAU,EAAE,QAAQ,EAAE;IACvCnT,UAAAA,UAAU,EAAE,oBAAoB;IAChCC,UAAAA,SAAS,EAAE,QAAQ;IACnBC,UAAAA,QAAQ,EAAE,aAAa;IACvBT,UAAAA,SAAS,EAAE,mBAAA;IACf,SAAC,CAAC,CAAA;IACN,OAAA;UACA,IAAIuT,MAAM,CAACI,aAAa,EAAE;YACtBjO,kBAAM,CAACZ,MAAM,CAACyO,MAAM,CAACI,aAAa,EAAE,QAAQ,EAAE;IAC1CpT,UAAAA,UAAU,EAAE,oBAAoB;IAChCC,UAAAA,SAAS,EAAE,QAAQ;IACnBC,UAAAA,QAAQ,EAAE,aAAa;IACvBT,UAAAA,SAAS,EAAE,sBAAA;IACf,SAAC,CAAC,CAAA;IACN,OAAA;IACJ,KAAA;QACA,IAAI,CAACmV,OAAO,GAAG5B,MAAM,CAAA;IACrB,IAAA,IAAI,CAACM,cAAc,GAAGN,MAAM,CAACI,aAAa,CAAA;IAC1C,IAAA,IAAI,CAACyB,iBAAiB,GAAG,IAAInO,GAAG,EAAE,CAAA;QAClC,IAAIsM,MAAM,CAAC8B,iBAAiB,EAAE;IAC1BlJ,MAAAA,0BAA0B,CAAC,MAAM,IAAI,CAACmJ,sBAAsB,EAAE,CAAC,CAAA;IACnE,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIN,mBAAmBA,CAAClR,SAAS,EAAE;IAC3B,IAAA,IAAIA,SAAS,KAAKyH,UAAU,CAACM,cAAc,EAAE,EAAE;IAC3C,MAAA,MAAM,IAAItH,YAAY,CAAC,2BAA2B,CAAC,CAAA;IACvD,KAAA;QACA,IAAIwQ,eAAe,GAAG,IAAI,CAACK,iBAAiB,CAACnM,GAAG,CAACnF,SAAS,CAAC,CAAA;QAC3D,IAAI,CAACiR,eAAe,EAAE;UAClBA,eAAe,GAAG,IAAIzB,eAAe,CAACxP,SAAS,EAAE,IAAI,CAACqR,OAAO,CAAC,CAAA;UAC9D,IAAI,CAACC,iBAAiB,CAACvL,GAAG,CAAC/F,SAAS,EAAEiR,eAAe,CAAC,CAAA;IAC1D,KAAA;IACA,IAAA,OAAOA,eAAe,CAAA;IAC1B,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;MACID,oBAAoBA,CAACF,cAAc,EAAE;IACjC,IAAA,IAAI,CAAC,IAAI,CAACf,cAAc,EAAE;IACtB;IACA,MAAA,OAAO,IAAI,CAAA;IACf,KAAA;IACA;IACA;IACA;IACA,IAAA,MAAM0B,mBAAmB,GAAG,IAAI,CAACC,uBAAuB,CAACZ,cAAc,CAAC,CAAA;QACxE,IAAIW,mBAAmB,KAAK,IAAI,EAAE;IAC9B;IACA,MAAA,OAAO,IAAI,CAAA;IACf,KAAA;IACA;IACA;IACA,IAAA,MAAMrB,GAAG,GAAGD,IAAI,CAACC,GAAG,EAAE,CAAA;QACtB,OAAOqB,mBAAmB,IAAIrB,GAAG,GAAG,IAAI,CAACL,cAAc,GAAG,IAAI,CAAA;IAClE,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI2B,uBAAuBA,CAACZ,cAAc,EAAE;QACpC,IAAI,CAACA,cAAc,CAACa,OAAO,CAACzM,GAAG,CAAC,MAAM,CAAC,EAAE;IACrC,MAAA,OAAO,IAAI,CAAA;IACf,KAAA;QACA,MAAM0M,UAAU,GAAGd,cAAc,CAACa,OAAO,CAACxM,GAAG,CAAC,MAAM,CAAC,CAAA;IACrD,IAAA,MAAM0M,UAAU,GAAG,IAAI1B,IAAI,CAACyB,UAAU,CAAC,CAAA;IACvC,IAAA,MAAME,UAAU,GAAGD,UAAU,CAACE,OAAO,EAAE,CAAA;IACvC;IACA;IACA,IAAA,IAAIC,KAAK,CAACF,UAAU,CAAC,EAAE;IACnB,MAAA,OAAO,IAAI,CAAA;IACf,KAAA;IACA,IAAA,OAAOA,UAAU,CAAA;IACrB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI,MAAMN,sBAAsBA,GAAG;IAC3B;IACA;QACA,KAAK,MAAM,CAACxR,SAAS,EAAEiR,eAAe,CAAC,IAAI,IAAI,CAACK,iBAAiB,EAAE;IAC/D,MAAA,MAAMnX,IAAI,CAACoW,MAAM,CAACjB,MAAM,CAACtP,SAAS,CAAC,CAAA;IACnC,MAAA,MAAMiR,eAAe,CAAC3B,MAAM,EAAE,CAAA;IAClC,KAAA;IACA;IACA,IAAA,IAAI,CAACgC,iBAAiB,GAAG,IAAInO,GAAG,EAAE,CAAA;IACtC,GAAA;IACJ;;IC3PA;IACA,IAAI;IACAhJ,EAAAA,IAAI,CAAC,kCAAkC,CAAC,IAAIC,CAAC,EAAE,CAAA;IACnD,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAMA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM4X,iBAAiB,CAAC;IACpB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIvR,EAAAA,WAAWA,CAAC+O,MAAM,GAAG,EAAE,EAAE;QACsB;UACvC,IAAI,EAAEA,MAAM,CAACyC,QAAQ,IAAIzC,MAAM,CAACkC,OAAO,CAAC,EAAE;IACtC,QAAA,MAAM,IAAIlR,YAAY,CAAC,8BAA8B,EAAE;IACnDhE,UAAAA,UAAU,EAAE,4BAA4B;IACxCC,UAAAA,SAAS,EAAE,mBAAmB;IAC9BC,UAAAA,QAAQ,EAAE,aAAA;IACd,SAAC,CAAC,CAAA;IACN,OAAA;UACA,IAAI8S,MAAM,CAACyC,QAAQ,EAAE;IACjBtQ,QAAAA,kBAAM,CAAChB,OAAO,CAAC6O,MAAM,CAACyC,QAAQ,EAAE;IAC5BzV,UAAAA,UAAU,EAAE,4BAA4B;IACxCC,UAAAA,SAAS,EAAE,mBAAmB;IAC9BC,UAAAA,QAAQ,EAAE,aAAa;IACvBT,UAAAA,SAAS,EAAE,iBAAA;IACf,SAAC,CAAC,CAAA;IACN,OAAA;UACA,IAAIuT,MAAM,CAACkC,OAAO,EAAE;YAChB/P,kBAAM,CAACZ,MAAM,CAACyO,MAAM,CAACkC,OAAO,EAAE,QAAQ,EAAE;IACpClV,UAAAA,UAAU,EAAE,4BAA4B;IACxCC,UAAAA,SAAS,EAAE,mBAAmB;IAC9BC,UAAAA,QAAQ,EAAE,aAAa;IACvBT,UAAAA,SAAS,EAAE,gBAAA;IACf,SAAC,CAAC,CAAA;IACN,OAAA;IACJ,KAAA;IACA,IAAA,IAAI,CAACiW,SAAS,GAAG1C,MAAM,CAACyC,QAAQ,CAAA;IAChC,IAAA,IAAI,CAACE,QAAQ,GAAG3C,MAAM,CAACkC,OAAO,CAAA;IAClC,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIU,mBAAmBA,CAACC,QAAQ,EAAE;QACiB;IACvC1Q,MAAAA,kBAAM,CAACX,UAAU,CAACqR,QAAQ,EAAEC,QAAQ,EAAE;IAClC9V,QAAAA,UAAU,EAAE,4BAA4B;IACxCC,QAAAA,SAAS,EAAE,mBAAmB;IAC9BC,QAAAA,QAAQ,EAAE,qBAAqB;IAC/BT,QAAAA,SAAS,EAAE,UAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,IAAIsW,SAAS,GAAG,IAAI,CAAA;QACpB,IAAI,IAAI,CAACL,SAAS,EAAE;UAChBK,SAAS,GAAG,IAAI,CAACL,SAAS,CAAC/Q,QAAQ,CAACkR,QAAQ,CAAC1S,MAAM,CAAC,CAAA;IACxD,KAAA;IACA,IAAA,IAAI,IAAI,CAACwS,QAAQ,IAAII,SAAS,EAAE;IAC5BA,MAAAA,SAAS,GAAG3W,MAAM,CAACC,IAAI,CAAC,IAAI,CAACsW,QAAQ,CAAC,CAAC1J,IAAI,CAAE+J,UAAU,IAAK;IACxD,QAAA,OAAOH,QAAQ,CAACX,OAAO,CAACxM,GAAG,CAACsN,UAAU,CAAC,KAAK,IAAI,CAACL,QAAQ,CAACK,UAAU,CAAC,CAAA;IACzE,OAAC,CAAC,CAAA;IACN,KAAA;QAC2C;UACvC,IAAI,CAACD,SAAS,EAAE;IACZlY,QAAAA,MAAM,CAACS,cAAc,CAAC,CAAA,gBAAA,CAAkB,GACpC,CAAI6H,CAAAA,EAAAA,cAAc,CAAC0P,QAAQ,CAAC/S,GAAG,CAAC,CAAkC,gCAAA,CAAA,GAClE,yCAAyC,CAAC,CAAA;IAC9CjF,QAAAA,MAAM,CAACS,cAAc,CAAC,CAAA,gCAAA,CAAkC,CAAC,CAAA;IACzDT,QAAAA,MAAM,CAACM,GAAG,CAAC,CAAA,oBAAA,CAAsB,GAAG0B,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC4V,SAAS,CAAC,CAAC,CAAA;IACnE7X,QAAAA,MAAM,CAACM,GAAG,CAAC,CAAqB,mBAAA,CAAA,GAAG0B,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC6V,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;YAC1E9X,MAAM,CAACU,QAAQ,EAAE,CAAA;YACjB,MAAM0X,kBAAkB,GAAG,EAAE,CAAA;YAC7BJ,QAAQ,CAACX,OAAO,CAACvM,OAAO,CAAC,CAAChJ,KAAK,EAAEL,GAAG,KAAK;IACrC2W,UAAAA,kBAAkB,CAAC3W,GAAG,CAAC,GAAGK,KAAK,CAAA;IACnC,SAAC,CAAC,CAAA;IACF9B,QAAAA,MAAM,CAACS,cAAc,CAAC,CAAA,sCAAA,CAAwC,CAAC,CAAA;YAC/DT,MAAM,CAACM,GAAG,CAAC,CAAA,iBAAA,EAAoB0X,QAAQ,CAAC1S,MAAM,EAAE,CAAC,CAAA;IACjDtF,QAAAA,MAAM,CAACM,GAAG,CAAC,CAAA,kBAAA,CAAoB,GAAG0B,IAAI,CAACC,SAAS,CAACmW,kBAAkB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;YAC9EpY,MAAM,CAACU,QAAQ,EAAE,CAAA;IACjBV,QAAAA,MAAM,CAACS,cAAc,CAAC,CAAA,gCAAA,CAAkC,CAAC,CAAA;IACzDT,QAAAA,MAAM,CAACM,GAAG,CAAC0X,QAAQ,CAACX,OAAO,CAAC,CAAA;IAC5BrX,QAAAA,MAAM,CAACM,GAAG,CAAC0X,QAAQ,CAAC,CAAA;YACpBhY,MAAM,CAACU,QAAQ,EAAE,CAAA;YACjBV,MAAM,CAACU,QAAQ,EAAE,CAAA;IACrB,OAAA;IACJ,KAAA;IACA,IAAA,OAAOwX,SAAS,CAAA;IACpB,GAAA;IACJ;;ICrHA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMG,uBAAuB,CAAC;IAC1B;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIjS,WAAWA,CAAC+O,MAAM,EAAE;IAChB;IACR;IACA;IACA;IACA;IACA;QACQ,IAAI,CAACmD,eAAe,GAAG,OAAO;IAAEN,MAAAA,QAAAA;IAAS,KAAC,KAAK;UAC3C,IAAI,IAAI,CAACO,kBAAkB,CAACR,mBAAmB,CAACC,QAAQ,CAAC,EAAE;IACvD,QAAA,OAAOA,QAAQ,CAAA;IACnB,OAAA;IACA,MAAA,OAAO,IAAI,CAAA;SACd,CAAA;IACD,IAAA,IAAI,CAACO,kBAAkB,GAAG,IAAIZ,iBAAiB,CAACxC,MAAM,CAAC,CAAA;IAC3D,GAAA;IACJ;;IC9CA;IACA;IACA;IACA;IACA;IACA;IAEA,SAASqD,WAAWA,CAACC,OAAO,EAAEC,YAAY,EAAE;IACxC,EAAA,MAAMC,WAAW,GAAG,IAAInQ,GAAG,CAACiQ,OAAO,CAAC,CAAA;IACpC,EAAA,KAAK,MAAMG,KAAK,IAAIF,YAAY,EAAE;IAC9BC,IAAAA,WAAW,CAACE,YAAY,CAAC7D,MAAM,CAAC4D,KAAK,CAAC,CAAA;IAC1C,GAAA;MACA,OAAOD,WAAW,CAAC1Q,IAAI,CAAA;IAC3B,CAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,eAAe6Q,sBAAsBA,CAAC9C,KAAK,EAAE7M,OAAO,EAAEuP,YAAY,EAAE/C,YAAY,EAAE;MAC9E,MAAMoD,kBAAkB,GAAGP,WAAW,CAACrP,OAAO,CAAClE,GAAG,EAAEyT,YAAY,CAAC,CAAA;IACjE;IACA,EAAA,IAAIvP,OAAO,CAAClE,GAAG,KAAK8T,kBAAkB,EAAE;IACpC,IAAA,OAAO/C,KAAK,CAACvO,KAAK,CAAC0B,OAAO,EAAEwM,YAAY,CAAC,CAAA;IAC7C,GAAA;IACA;IACA,EAAA,MAAMqD,WAAW,GAAGzX,MAAM,CAAC0X,MAAM,CAAC1X,MAAM,CAAC0X,MAAM,CAAC,EAAE,EAAEtD,YAAY,CAAC,EAAE;IAAEuD,IAAAA,YAAY,EAAE,IAAA;IAAK,GAAC,CAAC,CAAA;MAC1F,MAAMC,SAAS,GAAG,MAAMnD,KAAK,CAACxU,IAAI,CAAC2H,OAAO,EAAE6P,WAAW,CAAC,CAAA;IACxD,EAAA,KAAK,MAAMI,QAAQ,IAAID,SAAS,EAAE;QAC9B,MAAME,mBAAmB,GAAGb,WAAW,CAACY,QAAQ,CAACnU,GAAG,EAAEyT,YAAY,CAAC,CAAA;QACnE,IAAIK,kBAAkB,KAAKM,mBAAmB,EAAE;IAC5C,MAAA,OAAOrD,KAAK,CAACvO,KAAK,CAAC2R,QAAQ,EAAEzD,YAAY,CAAC,CAAA;IAC9C,KAAA;IACJ,GAAA;IACA,EAAA,OAAA;IACJ;;IC1CA;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM2D,QAAQ,CAAC;IACX;IACJ;IACA;IACIlT,EAAAA,WAAWA,GAAG;QACV,IAAI,CAACwH,OAAO,GAAG,IAAIhE,OAAO,CAAC,CAAC8F,OAAO,EAAEzE,MAAM,KAAK;UAC5C,IAAI,CAACyE,OAAO,GAAGA,OAAO,CAAA;UACtB,IAAI,CAACzE,MAAM,GAAGA,MAAM,CAAA;IACxB,KAAC,CAAC,CAAA;IACN,GAAA;IACJ;;IC1BA;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,eAAesO,0BAA0BA,GAAG;MACG;QACvCvZ,MAAM,CAACM,GAAG,CAAC,CAAgBuN,aAAAA,EAAAA,mBAAmB,CAAChJ,IAAI,CAAA,CAAA,CAAG,GAClD,CAAA,6BAAA,CAA+B,CAAC,CAAA;IACxC,GAAA;IACA,EAAA,KAAK,MAAMmJ,QAAQ,IAAIH,mBAAmB,EAAE;QACxC,MAAMG,QAAQ,EAAE,CAAA;QAC2B;IACvChO,MAAAA,MAAM,CAACM,GAAG,CAAC0N,QAAQ,EAAE,cAAc,CAAC,CAAA;IACxC,KAAA;IACJ,GAAA;MAC2C;IACvChO,IAAAA,MAAM,CAACM,GAAG,CAAC,6BAA6B,CAAC,CAAA;IAC7C,GAAA;IACJ;;IC/BA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,SAASkZ,OAAOA,CAACC,EAAE,EAAE;MACxB,OAAO,IAAI7P,OAAO,CAAE8F,OAAO,IAAKgK,UAAU,CAAChK,OAAO,EAAE+J,EAAE,CAAC,CAAC,CAAA;IAC5D;;IChBA;IACA,IAAI;IACA5Z,EAAAA,IAAI,CAAC,0BAA0B,CAAC,IAAIC,CAAC,EAAE,CAAA;IAC3C,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAUA,SAAS4Z,SAASA,CAACC,KAAK,EAAE;MACtB,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAAG,IAAI7P,OAAO,CAAC6P,KAAK,CAAC,GAAGA,KAAK,CAAA;IACjE,CAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,eAAe,CAAC;IAClB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIzT,EAAAA,WAAWA,CAAC0T,QAAQ,EAAEC,OAAO,EAAE;IAC3B,IAAA,IAAI,CAACC,UAAU,GAAG,EAAE,CAAA;IACpB;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACQ;IACR;IACA;IACA;IACA;IACA;IACA;IACQ;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACQ;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;QACmD;UACvC1S,kBAAM,CAACX,UAAU,CAACoT,OAAO,CAAC7Q,KAAK,EAAE+Q,eAAe,EAAE;IAC9C9X,QAAAA,UAAU,EAAE,oBAAoB;IAChCC,QAAAA,SAAS,EAAE,iBAAiB;IAC5BC,QAAAA,QAAQ,EAAE,aAAa;IACvBT,QAAAA,SAAS,EAAE,eAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;IACAL,IAAAA,MAAM,CAAC0X,MAAM,CAAC,IAAI,EAAEc,OAAO,CAAC,CAAA;IAC5B,IAAA,IAAI,CAAC7Q,KAAK,GAAG6Q,OAAO,CAAC7Q,KAAK,CAAA;QAC1B,IAAI,CAACgR,SAAS,GAAGJ,QAAQ,CAAA;IACzB,IAAA,IAAI,CAACK,gBAAgB,GAAG,IAAIb,QAAQ,EAAE,CAAA;QACtC,IAAI,CAACc,uBAAuB,GAAG,EAAE,CAAA;IACjC;IACA;QACA,IAAI,CAACC,QAAQ,GAAG,CAAC,GAAGP,QAAQ,CAACQ,OAAO,CAAC,CAAA;IACrC,IAAA,IAAI,CAACC,eAAe,GAAG,IAAI1R,GAAG,EAAE,CAAA;IAChC,IAAA,KAAK,MAAM2R,MAAM,IAAI,IAAI,CAACH,QAAQ,EAAE;UAChC,IAAI,CAACE,eAAe,CAAC9O,GAAG,CAAC+O,MAAM,EAAE,EAAE,CAAC,CAAA;IACxC,KAAA;QACA,IAAI,CAACtR,KAAK,CAACc,SAAS,CAAC,IAAI,CAACmQ,gBAAgB,CAACvM,OAAO,CAAC,CAAA;IACvD,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI,MAAM6M,KAAKA,CAACb,KAAK,EAAE;QACf,MAAM;IAAE1Q,MAAAA,KAAAA;IAAM,KAAC,GAAG,IAAI,CAAA;IACtB,IAAA,IAAIC,OAAO,GAAGwQ,SAAS,CAACC,KAAK,CAAC,CAAA;IAC9B,IAAA,IAAIzQ,OAAO,CAACuR,IAAI,KAAK,UAAU,IAC3BxR,KAAK,YAAYyR,UAAU,IAC3BzR,KAAK,CAAC0R,eAAe,EAAE;IACvB,MAAA,MAAMC,uBAAuB,GAAI,MAAM3R,KAAK,CAAC0R,eAAgB,CAAA;IAC7D,MAAA,IAAIC,uBAAuB,EAAE;YACkB;IACvC7a,UAAAA,MAAM,CAACM,GAAG,CAAC,CAAA,0CAAA,CAA4C,GACnD,CAAA,CAAA,EAAIgI,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,GAAG,CAAC,CAAA;IAC3C,SAAA;IACA,QAAA,OAAO4V,uBAAuB,CAAA;IAClC,OAAA;IACJ,KAAA;IACA;IACA;IACA;IACA,IAAA,MAAMC,eAAe,GAAG,IAAI,CAACC,WAAW,CAAC,cAAc,CAAC,GAClD5R,OAAO,CAAC6R,KAAK,EAAE,GACf,IAAI,CAAA;QACV,IAAI;UACA,KAAK,MAAMC,EAAE,IAAI,IAAI,CAACC,gBAAgB,CAAC,kBAAkB,CAAC,EAAE;YACxD/R,OAAO,GAAG,MAAM8R,EAAE,CAAC;IAAE9R,UAAAA,OAAO,EAAEA,OAAO,CAAC6R,KAAK,EAAE;IAAE9R,UAAAA,KAAAA;IAAM,SAAC,CAAC,CAAA;IAC3D,OAAA;SACH,CACD,OAAO8B,GAAG,EAAE;UACR,IAAIA,GAAG,YAAYjJ,KAAK,EAAE;IACtB,QAAA,MAAM,IAAIoE,YAAY,CAAC,iCAAiC,EAAE;cACtD/C,kBAAkB,EAAE4H,GAAG,CAAC5F,OAAAA;IAC5B,SAAC,CAAC,CAAA;IACN,OAAA;IACJ,KAAA;IACA;IACA;IACA;IACA,IAAA,MAAM+V,qBAAqB,GAAGhS,OAAO,CAAC6R,KAAK,EAAE,CAAA;QAC7C,IAAI;IACA,MAAA,IAAII,aAAa,CAAA;IACjB;IACAA,MAAAA,aAAa,GAAG,MAAMX,KAAK,CAACtR,OAAO,EAAEA,OAAO,CAACuR,IAAI,KAAK,UAAU,GAAGnP,SAAS,GAAG,IAAI,CAAC2O,SAAS,CAACmB,YAAY,CAAC,CAAA;UAC3G,IAAI,aAAoB,KAAK,YAAY,EAAE;IACvCrb,QAAAA,MAAM,CAACK,KAAK,CAAC,sBAAsB,GAC/B,CAAA,CAAA,EAAIiI,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,6BAA6B,GAC5D,CAAA,QAAA,EAAWmW,aAAa,CAAC9V,MAAM,IAAI,CAAC,CAAA;IAC5C,OAAA;UACA,KAAK,MAAM0I,QAAQ,IAAI,IAAI,CAACkN,gBAAgB,CAAC,iBAAiB,CAAC,EAAE;YAC7DE,aAAa,GAAG,MAAMpN,QAAQ,CAAC;cAC3B9E,KAAK;IACLC,UAAAA,OAAO,EAAEgS,qBAAqB;IAC9BnD,UAAAA,QAAQ,EAAEoD,aAAAA;IACd,SAAC,CAAC,CAAA;IACN,OAAA;IACA,MAAA,OAAOA,aAAa,CAAA;SACvB,CACD,OAAO5a,KAAK,EAAE;UACiC;IACvCR,QAAAA,MAAM,CAACM,GAAG,CAAC,CAAA,oBAAA,CAAsB,GAC7B,CAAIgI,CAAAA,EAAAA,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,CAAmB,iBAAA,CAAA,EAAEzE,KAAK,CAAC,CAAA;IAClE,OAAA;IACA;IACA;IACA,MAAA,IAAIsa,eAAe,EAAE;IACjB,QAAA,MAAM,IAAI,CAACQ,YAAY,CAAC,cAAc,EAAE;IACpC9a,UAAAA,KAAK,EAAEA,KAAK;cACZ0I,KAAK;IACL4R,UAAAA,eAAe,EAAEA,eAAe,CAACE,KAAK,EAAE;IACxC7R,UAAAA,OAAO,EAAEgS,qBAAqB,CAACH,KAAK,EAAC;IACzC,SAAC,CAAC,CAAA;IACN,OAAA;IACA,MAAA,MAAMxa,KAAK,CAAA;IACf,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI,MAAM+a,gBAAgBA,CAAC3B,KAAK,EAAE;QAC1B,MAAM5B,QAAQ,GAAG,MAAM,IAAI,CAACyC,KAAK,CAACb,KAAK,CAAC,CAAA;IACxC,IAAA,MAAM4B,aAAa,GAAGxD,QAAQ,CAACgD,KAAK,EAAE,CAAA;IACtC,IAAA,KAAK,IAAI,CAAChR,SAAS,CAAC,IAAI,CAACyR,QAAQ,CAAC7B,KAAK,EAAE4B,aAAa,CAAC,CAAC,CAAA;IACxD,IAAA,OAAOxD,QAAQ,CAAA;IACnB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI,MAAM0D,UAAUA,CAACja,GAAG,EAAE;IAClB,IAAA,MAAM0H,OAAO,GAAGwQ,SAAS,CAAClY,GAAG,CAAC,CAAA;IAC9B,IAAA,IAAI+U,cAAc,CAAA;QAClB,MAAM;UAAE9Q,SAAS;IAAEiQ,MAAAA,YAAAA;SAAc,GAAG,IAAI,CAACuE,SAAS,CAAA;QAClD,MAAMyB,gBAAgB,GAAG,MAAM,IAAI,CAACC,WAAW,CAACzS,OAAO,EAAE,MAAM,CAAC,CAAA;IAChE,IAAA,MAAM0S,iBAAiB,GAAGta,MAAM,CAAC0X,MAAM,CAAC1X,MAAM,CAAC0X,MAAM,CAAC,EAAE,EAAEtD,YAAY,CAAC,EAAE;IAAEjQ,MAAAA,SAAAA;IAAU,KAAC,CAAC,CAAA;QACvF8Q,cAAc,GAAG,MAAMP,MAAM,CAACxO,KAAK,CAACkU,gBAAgB,EAAEE,iBAAiB,CAAC,CAAA;QAC7B;IACvC,MAAA,IAAIrF,cAAc,EAAE;IAChBxW,QAAAA,MAAM,CAACK,KAAK,CAAC,CAA+BqF,4BAAAA,EAAAA,SAAS,IAAI,CAAC,CAAA;IAC9D,OAAC,MACI;IACD1F,QAAAA,MAAM,CAACK,KAAK,CAAC,CAAgCqF,6BAAAA,EAAAA,SAAS,IAAI,CAAC,CAAA;IAC/D,OAAA;IACJ,KAAA;QACA,KAAK,MAAMsI,QAAQ,IAAI,IAAI,CAACkN,gBAAgB,CAAC,0BAA0B,CAAC,EAAE;IACtE1E,MAAAA,cAAc,GACV,CAAC,MAAMxI,QAAQ,CAAC;YACZtI,SAAS;YACTiQ,YAAY;YACZa,cAAc;IACdrN,QAAAA,OAAO,EAAEwS,gBAAgB;YACzBzS,KAAK,EAAE,IAAI,CAACA,KAAAA;WACf,CAAC,KAAKqC,SAAS,CAAA;IACxB,KAAA;IACA,IAAA,OAAOiL,cAAc,CAAA;IACzB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMiF,QAAQA,CAACha,GAAG,EAAEuW,QAAQ,EAAE;IAC1B,IAAA,MAAM7O,OAAO,GAAGwQ,SAAS,CAAClY,GAAG,CAAC,CAAA;IAC9B;IACA;QACA,MAAM+X,OAAO,CAAC,CAAC,CAAC,CAAA;QAChB,MAAMmC,gBAAgB,GAAG,MAAM,IAAI,CAACC,WAAW,CAACzS,OAAO,EAAE,OAAO,CAAC,CAAA;QACtB;UACvC,IAAIwS,gBAAgB,CAAC/a,MAAM,IAAI+a,gBAAgB,CAAC/a,MAAM,KAAK,KAAK,EAAE;IAC9D,QAAA,MAAM,IAAIuF,YAAY,CAAC,kCAAkC,EAAE;IACvDlB,UAAAA,GAAG,EAAEqD,cAAc,CAACqT,gBAAgB,CAAC1W,GAAG,CAAC;cACzCrE,MAAM,EAAE+a,gBAAgB,CAAC/a,MAAAA;IAC7B,SAAC,CAAC,CAAA;IACN,OAAA;IACA;UACA,MAAMkb,IAAI,GAAG9D,QAAQ,CAACX,OAAO,CAACxM,GAAG,CAAC,MAAM,CAAC,CAAA;IACzC,MAAA,IAAIiR,IAAI,EAAE;IACN9b,QAAAA,MAAM,CAACK,KAAK,CAAC,oBAAoBiI,cAAc,CAACqT,gBAAgB,CAAC1W,GAAG,CAAC,CAAG,CAAA,CAAA,GACpE,gBAAgB6W,IAAI,CAAA,UAAA,CAAY,GAChC,CAAkE,gEAAA,CAAA,GAClE,0DAA0D,CAAC,CAAA;IACnE,OAAA;IACJ,KAAA;QACA,IAAI,CAAC9D,QAAQ,EAAE;UACgC;IACvChY,QAAAA,MAAM,CAACQ,KAAK,CAAC,CAAA,uCAAA,CAAyC,GAClD,CAAA,CAAA,EAAI8H,cAAc,CAACqT,gBAAgB,CAAC1W,GAAG,CAAC,IAAI,CAAC,CAAA;IACrD,OAAA;IACA,MAAA,MAAM,IAAIkB,YAAY,CAAC,4BAA4B,EAAE;IACjDlB,QAAAA,GAAG,EAAEqD,cAAc,CAACqT,gBAAgB,CAAC1W,GAAG,CAAA;IAC5C,OAAC,CAAC,CAAA;IACN,KAAA;QACA,MAAM8W,eAAe,GAAG,MAAM,IAAI,CAACC,0BAA0B,CAAChE,QAAQ,CAAC,CAAA;QACvE,IAAI,CAAC+D,eAAe,EAAE;UACyB;IACvC/b,QAAAA,MAAM,CAACK,KAAK,CAAC,CAAA,UAAA,EAAaiI,cAAc,CAACqT,gBAAgB,CAAC1W,GAAG,CAAC,CAAI,EAAA,CAAA,GAC9D,CAAqB,mBAAA,CAAA,EAAE8W,eAAe,CAAC,CAAA;IAC/C,OAAA;IACA,MAAA,OAAO,KAAK,CAAA;IAChB,KAAA;QACA,MAAM;UAAErW,SAAS;IAAEiQ,MAAAA,YAAAA;SAAc,GAAG,IAAI,CAACuE,SAAS,CAAA;QAClD,MAAMlE,KAAK,GAAG,MAAMnW,IAAI,CAACoW,MAAM,CAACnE,IAAI,CAACpM,SAAS,CAAC,CAAA;IAC/C,IAAA,MAAMuW,sBAAsB,GAAG,IAAI,CAAClB,WAAW,CAAC,gBAAgB,CAAC,CAAA;IACjE,IAAA,MAAMmB,WAAW,GAAGD,sBAAsB,GACpC,MAAMnD,sBAAsB;IAC9B;IACA;IACA;IACA9C,IAAAA,KAAK,EAAE2F,gBAAgB,CAACX,KAAK,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAErF,YAAY,CAAC,GACjE,IAAI,CAAA;QACiC;IACvC3V,MAAAA,MAAM,CAACK,KAAK,CAAC,CAAA,cAAA,EAAiBqF,SAAS,CAA8B,4BAAA,CAAA,GACjE,CAAO4C,IAAAA,EAAAA,cAAc,CAACqT,gBAAgB,CAAC1W,GAAG,CAAC,GAAG,CAAC,CAAA;IACvD,KAAA;QACA,IAAI;IACA,MAAA,MAAM+Q,KAAK,CAAC1B,GAAG,CAACqH,gBAAgB,EAAEM,sBAAsB,GAAGF,eAAe,CAACf,KAAK,EAAE,GAAGe,eAAe,CAAC,CAAA;SACxG,CACD,OAAOvb,KAAK,EAAE;UACV,IAAIA,KAAK,YAAYuB,KAAK,EAAE;IACxB;IACA,QAAA,IAAIvB,KAAK,CAACkD,IAAI,KAAK,oBAAoB,EAAE;cACrC,MAAM6V,0BAA0B,EAAE,CAAA;IACtC,SAAA;IACA,QAAA,MAAM/Y,KAAK,CAAA;IACf,OAAA;IACJ,KAAA;QACA,KAAK,MAAMwN,QAAQ,IAAI,IAAI,CAACkN,gBAAgB,CAAC,gBAAgB,CAAC,EAAE;IAC5D,MAAA,MAAMlN,QAAQ,CAAC;YACXtI,SAAS;YACTwW,WAAW;IACXC,QAAAA,WAAW,EAAEJ,eAAe,CAACf,KAAK,EAAE;IACpC7R,QAAAA,OAAO,EAAEwS,gBAAgB;YACzBzS,KAAK,EAAE,IAAI,CAACA,KAAAA;IAChB,OAAC,CAAC,CAAA;IACN,KAAA;IACA,IAAA,OAAO,IAAI,CAAA;IACf,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAM0S,WAAWA,CAACzS,OAAO,EAAEuR,IAAI,EAAE;QAC7B,MAAMjZ,GAAG,GAAG,CAAG0H,EAAAA,OAAO,CAAClE,GAAG,CAAA,GAAA,EAAMyV,IAAI,CAAE,CAAA,CAAA;IACtC,IAAA,IAAI,CAAC,IAAI,CAACV,UAAU,CAACvY,GAAG,CAAC,EAAE;UACvB,IAAIka,gBAAgB,GAAGxS,OAAO,CAAA;UAC9B,KAAK,MAAM6E,QAAQ,IAAI,IAAI,CAACkN,gBAAgB,CAAC,oBAAoB,CAAC,EAAE;IAChES,QAAAA,gBAAgB,GAAGhC,SAAS,CAAC,MAAM3L,QAAQ,CAAC;cACxC0M,IAAI;IACJvR,UAAAA,OAAO,EAAEwS,gBAAgB;cACzBzS,KAAK,EAAE,IAAI,CAACA,KAAK;IACjB;IACAqB,UAAAA,MAAM,EAAE,IAAI,CAACA,MAAM;IACvB,SAAC,CAAC,CAAC,CAAA;IACP,OAAA;IACA,MAAA,IAAI,CAACyP,UAAU,CAACvY,GAAG,CAAC,GAAGka,gBAAgB,CAAA;IAC3C,KAAA;IACA,IAAA,OAAO,IAAI,CAAC3B,UAAU,CAACvY,GAAG,CAAC,CAAA;IAC/B,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;MACIsZ,WAAWA,CAACrX,IAAI,EAAE;QACd,KAAK,MAAM8W,MAAM,IAAI,IAAI,CAACN,SAAS,CAACI,OAAO,EAAE;UACzC,IAAI5W,IAAI,IAAI8W,MAAM,EAAE;IAChB,QAAA,OAAO,IAAI,CAAA;IACf,OAAA;IACJ,KAAA;IACA,IAAA,OAAO,KAAK,CAAA;IAChB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMc,YAAYA,CAAC5X,IAAI,EAAEkV,KAAK,EAAE;QAC5B,KAAK,MAAM5K,QAAQ,IAAI,IAAI,CAACkN,gBAAgB,CAACxX,IAAI,CAAC,EAAE;IAChD;IACA;UACA,MAAMsK,QAAQ,CAAC4K,KAAK,CAAC,CAAA;IACzB,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI,CAACsC,gBAAgBA,CAACxX,IAAI,EAAE;QACpB,KAAK,MAAM8W,MAAM,IAAI,IAAI,CAACN,SAAS,CAACI,OAAO,EAAE;IACzC,MAAA,IAAI,OAAOE,MAAM,CAAC9W,IAAI,CAAC,KAAK,UAAU,EAAE;YACpC,MAAM0Y,KAAK,GAAG,IAAI,CAAC7B,eAAe,CAAC1P,GAAG,CAAC2P,MAAM,CAAC,CAAA;YAC9C,MAAM6B,gBAAgB,GAAIzD,KAAK,IAAK;IAChC,UAAA,MAAM0D,aAAa,GAAG/a,MAAM,CAAC0X,MAAM,CAAC1X,MAAM,CAAC0X,MAAM,CAAC,EAAE,EAAEL,KAAK,CAAC,EAAE;IAAEwD,YAAAA,KAAAA;IAAM,WAAC,CAAC,CAAA;IACxE;IACA;IACA,UAAA,OAAO5B,MAAM,CAAC9W,IAAI,CAAC,CAAC4Y,aAAa,CAAC,CAAA;aACrC,CAAA;IACD,QAAA,MAAMD,gBAAgB,CAAA;IAC1B,OAAA;IACJ,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIrS,SAASA,CAAC4D,OAAO,EAAE;IACf,IAAA,IAAI,CAACwM,uBAAuB,CAACzP,IAAI,CAACiD,OAAO,CAAC,CAAA;IAC1C,IAAA,OAAOA,OAAO,CAAA;IAClB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI,MAAM2O,WAAWA,GAAG;IAChB,IAAA,IAAI3O,OAAO,CAAA;QACX,OAAQA,OAAO,GAAG,IAAI,CAACwM,uBAAuB,CAACtH,KAAK,EAAE,EAAG;IACrD,MAAA,MAAMlF,OAAO,CAAA;IACjB,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACI4O,EAAAA,OAAOA,GAAG;IACN,IAAA,IAAI,CAACrC,gBAAgB,CAACzK,OAAO,CAAC,IAAI,CAAC,CAAA;IACvC,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI,MAAMsM,0BAA0BA,CAAChE,QAAQ,EAAE;QACvC,IAAI+D,eAAe,GAAG/D,QAAQ,CAAA;QAC9B,IAAIyE,WAAW,GAAG,KAAK,CAAA;QACvB,KAAK,MAAMzO,QAAQ,IAAI,IAAI,CAACkN,gBAAgB,CAAC,iBAAiB,CAAC,EAAE;IAC7Da,MAAAA,eAAe,GACX,CAAC,MAAM/N,QAAQ,CAAC;YACZ7E,OAAO,EAAE,IAAI,CAACA,OAAO;IACrB6O,QAAAA,QAAQ,EAAE+D,eAAe;YACzB7S,KAAK,EAAE,IAAI,CAACA,KAAAA;WACf,CAAC,KAAKqC,SAAS,CAAA;IACpBkR,MAAAA,WAAW,GAAG,IAAI,CAAA;UAClB,IAAI,CAACV,eAAe,EAAE;IAClB,QAAA,MAAA;IACJ,OAAA;IACJ,KAAA;QACA,IAAI,CAACU,WAAW,EAAE;IACd,MAAA,IAAIV,eAAe,IAAIA,eAAe,CAACzW,MAAM,KAAK,GAAG,EAAE;IACnDyW,QAAAA,eAAe,GAAGxQ,SAAS,CAAA;IAC/B,OAAA;UAC2C;IACvC,QAAA,IAAIwQ,eAAe,EAAE;IACjB,UAAA,IAAIA,eAAe,CAACzW,MAAM,KAAK,GAAG,EAAE;IAChC,YAAA,IAAIyW,eAAe,CAACzW,MAAM,KAAK,CAAC,EAAE;IAC9BtF,cAAAA,MAAM,CAACO,IAAI,CAAC,CAAA,kBAAA,EAAqB,IAAI,CAAC4I,OAAO,CAAClE,GAAG,CAAI,EAAA,CAAA,GACjD,CAA0D,wDAAA,CAAA,GAC1D,mDAAmD,CAAC,CAAA;IAC5D,aAAC,MACI;IACDjF,cAAAA,MAAM,CAACK,KAAK,CAAC,qBAAqB,IAAI,CAAC8I,OAAO,CAAClE,GAAG,CAAI,EAAA,CAAA,GAClD,8BAA8B+S,QAAQ,CAAC1S,MAAM,CAAc,YAAA,CAAA,GAC3D,wBAAwB,CAAC,CAAA;IACjC,aAAA;IACJ,WAAA;IACJ,SAAA;IACJ,OAAA;IACJ,KAAA;IACA,IAAA,OAAOyW,eAAe,CAAA;IAC1B,GAAA;IACJ;;ICngBA;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA,MAAMW,QAAQ,CAAC;IACX;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACItW,EAAAA,WAAWA,CAAC2T,OAAO,GAAG,EAAE,EAAE;IACtB;IACR;IACA;IACA;IACA;IACA;IACA;QACQ,IAAI,CAACrU,SAAS,GAAGyH,UAAU,CAACM,cAAc,CAACsM,OAAO,CAACrU,SAAS,CAAC,CAAA;IAC7D;IACR;IACA;IACA;IACA;IACA;IACA;IACQ,IAAA,IAAI,CAAC4U,OAAO,GAAGP,OAAO,CAACO,OAAO,IAAI,EAAE,CAAA;IACpC;IACR;IACA;IACA;IACA;IACA;IACA;IACQ,IAAA,IAAI,CAACe,YAAY,GAAGtB,OAAO,CAACsB,YAAY,CAAA;IACxC;IACR;IACA;IACA;IACA;IACA;IACA;IACQ,IAAA,IAAI,CAAC1F,YAAY,GAAGoE,OAAO,CAACpE,YAAY,CAAA;IAC5C,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIpO,MAAMA,CAACwS,OAAO,EAAE;QACZ,MAAM,CAAC4C,YAAY,CAAC,GAAG,IAAI,CAACC,SAAS,CAAC7C,OAAO,CAAC,CAAA;IAC9C,IAAA,OAAO4C,YAAY,CAAA;IACvB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIC,SAASA,CAAC7C,OAAO,EAAE;IACf;QACA,IAAIA,OAAO,YAAYY,UAAU,EAAE;IAC/BZ,MAAAA,OAAO,GAAG;IACN7Q,QAAAA,KAAK,EAAE6Q,OAAO;YACd5Q,OAAO,EAAE4Q,OAAO,CAAC5Q,OAAAA;WACpB,CAAA;IACL,KAAA;IACA,IAAA,MAAMD,KAAK,GAAG6Q,OAAO,CAAC7Q,KAAK,CAAA;IAC3B,IAAA,MAAMC,OAAO,GAAG,OAAO4Q,OAAO,CAAC5Q,OAAO,KAAK,QAAQ,GAC7C,IAAIY,OAAO,CAACgQ,OAAO,CAAC5Q,OAAO,CAAC,GAC5B4Q,OAAO,CAAC5Q,OAAO,CAAA;QACrB,MAAMoB,MAAM,GAAG,QAAQ,IAAIwP,OAAO,GAAGA,OAAO,CAACxP,MAAM,GAAGgB,SAAS,CAAA;IAC/D,IAAA,MAAMlE,OAAO,GAAG,IAAIwS,eAAe,CAAC,IAAI,EAAE;UAAE3Q,KAAK;UAAEC,OAAO;IAAEoB,MAAAA,MAAAA;IAAO,KAAC,CAAC,CAAA;QACrE,MAAMoS,YAAY,GAAG,IAAI,CAACE,YAAY,CAACxV,OAAO,EAAE8B,OAAO,EAAED,KAAK,CAAC,CAAA;IAC/D,IAAA,MAAM4T,WAAW,GAAG,IAAI,CAACC,cAAc,CAACJ,YAAY,EAAEtV,OAAO,EAAE8B,OAAO,EAAED,KAAK,CAAC,CAAA;IAC9E;IACA,IAAA,OAAO,CAACyT,YAAY,EAAEG,WAAW,CAAC,CAAA;IACtC,GAAA;IACA,EAAA,MAAMD,YAAYA,CAACxV,OAAO,EAAE8B,OAAO,EAAED,KAAK,EAAE;IACxC,IAAA,MAAM7B,OAAO,CAACiU,YAAY,CAAC,kBAAkB,EAAE;UAAEpS,KAAK;IAAEC,MAAAA,OAAAA;IAAQ,KAAC,CAAC,CAAA;QAClE,IAAI6O,QAAQ,GAAGzM,SAAS,CAAA;QACxB,IAAI;UACAyM,QAAQ,GAAG,MAAM,IAAI,CAACgF,OAAO,CAAC7T,OAAO,EAAE9B,OAAO,CAAC,CAAA;IAC/C;IACA;IACA;UACA,IAAI,CAAC2Q,QAAQ,IAAIA,QAAQ,CAAClS,IAAI,KAAK,OAAO,EAAE;IACxC,QAAA,MAAM,IAAIK,YAAY,CAAC,aAAa,EAAE;cAAElB,GAAG,EAAEkE,OAAO,CAAClE,GAAAA;IAAI,SAAC,CAAC,CAAA;IAC/D,OAAA;SACH,CACD,OAAOzE,KAAK,EAAE;UACV,IAAIA,KAAK,YAAYuB,KAAK,EAAE;YACxB,KAAK,MAAMiM,QAAQ,IAAI3G,OAAO,CAAC6T,gBAAgB,CAAC,iBAAiB,CAAC,EAAE;cAChElD,QAAQ,GAAG,MAAMhK,QAAQ,CAAC;gBAAExN,KAAK;gBAAE0I,KAAK;IAAEC,YAAAA,OAAAA;IAAQ,WAAC,CAAC,CAAA;IACpD,UAAA,IAAI6O,QAAQ,EAAE;IACV,YAAA,MAAA;IACJ,WAAA;IACJ,SAAA;IACJ,OAAA;UACA,IAAI,CAACA,QAAQ,EAAE;IACX,QAAA,MAAMxX,KAAK,CAAA;IACf,OAAC,MAC+C;YAC5CR,MAAM,CAACM,GAAG,CAAC,CAAwBgI,qBAAAA,EAAAA,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,CAAA,GAAA,CAAK,GAC/D,CAAA,GAAA,EAAMzE,KAAK,YAAYuB,KAAK,GAAGvB,KAAK,CAAC4H,QAAQ,EAAE,GAAG,EAAE,CAAA,uDAAA,CAAyD,GAC7G,CAAA,yBAAA,CAA2B,CAAC,CAAA;IACpC,OAAA;IACJ,KAAA;QACA,KAAK,MAAM4F,QAAQ,IAAI3G,OAAO,CAAC6T,gBAAgB,CAAC,oBAAoB,CAAC,EAAE;UACnElD,QAAQ,GAAG,MAAMhK,QAAQ,CAAC;YAAE9E,KAAK;YAAEC,OAAO;IAAE6O,QAAAA,QAAAA;IAAS,OAAC,CAAC,CAAA;IAC3D,KAAA;IACA,IAAA,OAAOA,QAAQ,CAAA;IACnB,GAAA;MACA,MAAM+E,cAAcA,CAACJ,YAAY,EAAEtV,OAAO,EAAE8B,OAAO,EAAED,KAAK,EAAE;IACxD,IAAA,IAAI8O,QAAQ,CAAA;IACZ,IAAA,IAAIxX,KAAK,CAAA;QACT,IAAI;UACAwX,QAAQ,GAAG,MAAM2E,YAAY,CAAA;SAChC,CACD,OAAOnc,KAAK,EAAE;IACV;IACA;IACA;IAAA,KAAA;QAEJ,IAAI;IACA,MAAA,MAAM6G,OAAO,CAACiU,YAAY,CAAC,mBAAmB,EAAE;YAC5CpS,KAAK;YACLC,OAAO;IACP6O,QAAAA,QAAAA;IACJ,OAAC,CAAC,CAAA;IACF,MAAA,MAAM3Q,OAAO,CAACkV,WAAW,EAAE,CAAA;SAC9B,CACD,OAAOU,cAAc,EAAE;UACnB,IAAIA,cAAc,YAAYlb,KAAK,EAAE;IACjCvB,QAAAA,KAAK,GAAGyc,cAAc,CAAA;IAC1B,OAAA;IACJ,KAAA;IACA,IAAA,MAAM5V,OAAO,CAACiU,YAAY,CAAC,oBAAoB,EAAE;UAC7CpS,KAAK;UACLC,OAAO;UACP6O,QAAQ;IACRxX,MAAAA,KAAK,EAAEA,KAAAA;IACX,KAAC,CAAC,CAAA;QACF6G,OAAO,CAACmV,OAAO,EAAE,CAAA;IACjB,IAAA,IAAIhc,KAAK,EAAE;IACP,MAAA,MAAMA,KAAK,CAAA;IACf,KAAA;IACJ,GAAA;IACJ,CAAA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;ICnOA;IACA;AACA;IACA;IACA;IACA;IACA;IAIO,MAAMkB,QAAQ,GAAG;IACpBwb,EAAAA,aAAa,EAAEA,CAACC,YAAY,EAAEhU,OAAO,KAAK,CAAA,MAAA,EAASgU,YAAY,CAAA,gBAAA,EAAmB7U,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,CAAG,CAAA,CAAA;MAChHmY,kBAAkB,EAAGpF,QAAQ,IAAK;IAC9B,IAAA,IAAIA,QAAQ,EAAE;IACVhY,MAAAA,MAAM,CAACS,cAAc,CAAC,CAAA,6BAAA,CAA+B,CAAC,CAAA;IACtDT,MAAAA,MAAM,CAACM,GAAG,CAAC0X,QAAQ,IAAI,wBAAwB,CAAC,CAAA;UAChDhY,MAAM,CAACU,QAAQ,EAAE,CAAA;IACrB,KAAA;IACJ,GAAA;IACJ,CAAC;;ICnBD;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM2c,UAAU,SAASX,QAAQ,CAAC;IAC9B;IACJ;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMM,OAAOA,CAAC7T,OAAO,EAAE9B,OAAO,EAAE;QAC5B,MAAMiW,IAAI,GAAG,EAAE,CAAA;QAC4B;IACvChW,MAAAA,kBAAM,CAACX,UAAU,CAACwC,OAAO,EAAEY,OAAO,EAAE;IAChC5H,QAAAA,UAAU,EAAE,oBAAoB;IAChCC,QAAAA,SAAS,EAAE,IAAI,CAACgE,WAAW,CAAC1C,IAAI;IAChCrB,QAAAA,QAAQ,EAAE,aAAa;IACvBT,QAAAA,SAAS,EAAE,SAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,IAAIoW,QAAQ,GAAG,MAAM3Q,OAAO,CAACqU,UAAU,CAACvS,OAAO,CAAC,CAAA;QAChD,IAAI3I,KAAK,GAAG+K,SAAS,CAAA;QACrB,IAAI,CAACyM,QAAQ,EAAE;UACgC;YACvCsF,IAAI,CAAC3S,IAAI,CAAC,CAA6B,0BAAA,EAAA,IAAI,CAACjF,SAAS,CAAA,SAAA,CAAW,GAC5D,CAAA,oCAAA,CAAsC,CAAC,CAAA;IAC/C,OAAA;UACA,IAAI;IACAsS,QAAAA,QAAQ,GAAG,MAAM3Q,OAAO,CAACkU,gBAAgB,CAACpS,OAAO,CAAC,CAAA;WACrD,CACD,OAAO6B,GAAG,EAAE;YACR,IAAIA,GAAG,YAAYjJ,KAAK,EAAE;IACtBvB,UAAAA,KAAK,GAAGwK,GAAG,CAAA;IACf,SAAA;IACJ,OAAA;UAC2C;IACvC,QAAA,IAAIgN,QAAQ,EAAE;IACVsF,UAAAA,IAAI,CAAC3S,IAAI,CAAC,CAAA,0BAAA,CAA4B,CAAC,CAAA;IAC3C,SAAC,MACI;IACD2S,UAAAA,IAAI,CAAC3S,IAAI,CAAC,CAAA,0CAAA,CAA4C,CAAC,CAAA;IAC3D,SAAA;IACJ,OAAA;IACJ,KAAC,MACI;UAC0C;YACvC2S,IAAI,CAAC3S,IAAI,CAAC,CAAA,gCAAA,EAAmC,IAAI,CAACjF,SAAS,UAAU,CAAC,CAAA;IAC1E,OAAA;IACJ,KAAA;QAC2C;IACvC1F,MAAAA,MAAM,CAACS,cAAc,CAACiB,QAAQ,CAACwb,aAAa,CAAC,IAAI,CAAC9W,WAAW,CAAC1C,IAAI,EAAEyF,OAAO,CAAC,CAAC,CAAA;IAC7E,MAAA,KAAK,MAAM7I,GAAG,IAAIgd,IAAI,EAAE;IACpBtd,QAAAA,MAAM,CAACM,GAAG,CAACA,GAAG,CAAC,CAAA;IACnB,OAAA;IACAoB,MAAAA,QAAQ,CAAC0b,kBAAkB,CAACpF,QAAQ,CAAC,CAAA;UACrChY,MAAM,CAACU,QAAQ,EAAE,CAAA;IACrB,KAAA;QACA,IAAI,CAACsX,QAAQ,EAAE;IACX,MAAA,MAAM,IAAI7R,YAAY,CAAC,aAAa,EAAE;YAAElB,GAAG,EAAEkE,OAAO,CAAClE,GAAG;IAAEzE,QAAAA,KAAAA;IAAM,OAAC,CAAC,CAAA;IACtE,KAAA;IACA,IAAA,OAAOwX,QAAQ,CAAA;IACnB,GAAA;IACJ;;ICvFA;IACA;AACA;IACA;IACA;IACA;IACA;IAEO,MAAMuF,sBAAsB,GAAG;IAClC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIjF,eAAe,EAAE,OAAO;IAAEN,IAAAA,QAAAA;IAAS,GAAC,KAAK;QACrC,IAAIA,QAAQ,CAAC1S,MAAM,KAAK,GAAG,IAAI0S,QAAQ,CAAC1S,MAAM,KAAK,CAAC,EAAE;IAClD,MAAA,OAAO0S,QAAQ,CAAA;IACnB,KAAA;IACA,IAAA,OAAO,IAAI,CAAA;IACf,GAAA;IACJ,CAAC;;ICzBD;IACA;AACA;IACA;IACA;IACA;IACA;IAQA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMwF,YAAY,SAASd,QAAQ,CAAC;IAChC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACItW,EAAAA,WAAWA,CAAC2T,OAAO,GAAG,EAAE,EAAE;QACtB,KAAK,CAACA,OAAO,CAAC,CAAA;IACd;IACA;IACA,IAAA,IAAI,CAAC,IAAI,CAACO,OAAO,CAAClM,IAAI,CAAEqP,CAAC,IAAK,iBAAiB,IAAIA,CAAC,CAAC,EAAE;IACnD,MAAA,IAAI,CAACnD,OAAO,CAACoD,OAAO,CAACH,sBAAsB,CAAC,CAAA;IAChD,KAAA;IACA,IAAA,IAAI,CAACI,sBAAsB,GAAG5D,OAAO,CAAC6D,qBAAqB,IAAI,CAAC,CAAA;QACrB;UACvC,IAAI,IAAI,CAACD,sBAAsB,EAAE;YAC7BrW,kBAAM,CAACZ,MAAM,CAAC,IAAI,CAACiX,sBAAsB,EAAE,QAAQ,EAAE;IACjDxb,UAAAA,UAAU,EAAE,oBAAoB;IAChCC,UAAAA,SAAS,EAAE,IAAI,CAACgE,WAAW,CAAC1C,IAAI;IAChCrB,UAAAA,QAAQ,EAAE,aAAa;IACvBT,UAAAA,SAAS,EAAE,uBAAA;IACf,SAAC,CAAC,CAAA;IACN,OAAA;IACJ,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMob,OAAOA,CAAC7T,OAAO,EAAE9B,OAAO,EAAE;QAC5B,MAAMiW,IAAI,GAAG,EAAE,CAAA;QAC4B;IACvChW,MAAAA,kBAAM,CAACX,UAAU,CAACwC,OAAO,EAAEY,OAAO,EAAE;IAChC5H,QAAAA,UAAU,EAAE,oBAAoB;IAChCC,QAAAA,SAAS,EAAE,IAAI,CAACgE,WAAW,CAAC1C,IAAI;IAChCrB,QAAAA,QAAQ,EAAE,QAAQ;IAClBT,QAAAA,SAAS,EAAE,aAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,MAAMic,QAAQ,GAAG,EAAE,CAAA;IACnB,IAAA,IAAIC,SAAS,CAAA;QACb,IAAI,IAAI,CAACH,sBAAsB,EAAE;UAC7B,MAAM;YAAEzJ,EAAE;IAAEtG,QAAAA,OAAAA;IAAQ,OAAC,GAAG,IAAI,CAACmQ,kBAAkB,CAAC;YAAE5U,OAAO;YAAEmU,IAAI;IAAEjW,QAAAA,OAAAA;IAAQ,OAAC,CAAC,CAAA;IAC3EyW,MAAAA,SAAS,GAAG5J,EAAE,CAAA;IACd2J,MAAAA,QAAQ,CAAClT,IAAI,CAACiD,OAAO,CAAC,CAAA;IAC1B,KAAA;IACA,IAAA,MAAMoQ,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAAC;UAC3CH,SAAS;UACT3U,OAAO;UACPmU,IAAI;IACJjW,MAAAA,OAAAA;IACJ,KAAC,CAAC,CAAA;IACFwW,IAAAA,QAAQ,CAAClT,IAAI,CAACqT,cAAc,CAAC,CAAA;QAC7B,MAAMhG,QAAQ,GAAG,MAAM3Q,OAAO,CAAC2C,SAAS,CAAC,CAAC,YAAY;IAClD;IACA,MAAA,OAAQ,CAAC,MAAM3C,OAAO,CAAC2C,SAAS,CAACJ,OAAO,CAACsU,IAAI,CAACL,QAAQ,CAAC,CAAC;IACpD;IACA;IACA;IACA;IACA;IACC,MAAA,MAAMG,cAAc,CAAC,CAAA;SAC7B,GAAG,CAAC,CAAA;QACsC;IACvChe,MAAAA,MAAM,CAACS,cAAc,CAACiB,QAAQ,CAACwb,aAAa,CAAC,IAAI,CAAC9W,WAAW,CAAC1C,IAAI,EAAEyF,OAAO,CAAC,CAAC,CAAA;IAC7E,MAAA,KAAK,MAAM7I,GAAG,IAAIgd,IAAI,EAAE;IACpBtd,QAAAA,MAAM,CAACM,GAAG,CAACA,GAAG,CAAC,CAAA;IACnB,OAAA;IACAoB,MAAAA,QAAQ,CAAC0b,kBAAkB,CAACpF,QAAQ,CAAC,CAAA;UACrChY,MAAM,CAACU,QAAQ,EAAE,CAAA;IACrB,KAAA;QACA,IAAI,CAACsX,QAAQ,EAAE;IACX,MAAA,MAAM,IAAI7R,YAAY,CAAC,aAAa,EAAE;YAAElB,GAAG,EAAEkE,OAAO,CAAClE,GAAAA;IAAI,OAAC,CAAC,CAAA;IAC/D,KAAA;IACA,IAAA,OAAO+S,QAAQ,CAAA;IACnB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI+F,EAAAA,kBAAkBA,CAAC;QAAE5U,OAAO;QAAEmU,IAAI;IAAEjW,IAAAA,OAAAA;IAAS,GAAC,EAAE;IAC5C,IAAA,IAAIyW,SAAS,CAAA;IACb,IAAA,MAAMK,cAAc,GAAG,IAAIvU,OAAO,CAAE8F,OAAO,IAAK;IAC5C,MAAA,MAAM0O,gBAAgB,GAAG,YAAY;YACU;cACvCd,IAAI,CAAC3S,IAAI,CAAC,CAAqC,mCAAA,CAAA,GAC3C,GAAG,IAAI,CAACgT,sBAAsB,CAAA,SAAA,CAAW,CAAC,CAAA;IAClD,SAAA;YACAjO,OAAO,CAAC,MAAMrI,OAAO,CAACqU,UAAU,CAACvS,OAAO,CAAC,CAAC,CAAA;WAC7C,CAAA;UACD2U,SAAS,GAAGpE,UAAU,CAAC0E,gBAAgB,EAAE,IAAI,CAACT,sBAAsB,GAAG,IAAI,CAAC,CAAA;IAChF,KAAC,CAAC,CAAA;QACF,OAAO;IACH/P,MAAAA,OAAO,EAAEuQ,cAAc;IACvBjK,MAAAA,EAAE,EAAE4J,SAAAA;SACP,CAAA;IACL,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMG,kBAAkBA,CAAC;QAAEH,SAAS;QAAE3U,OAAO;QAAEmU,IAAI;IAAEjW,IAAAA,OAAAA;IAAS,GAAC,EAAE;IAC7D,IAAA,IAAI7G,KAAK,CAAA;IACT,IAAA,IAAIwX,QAAQ,CAAA;QACZ,IAAI;IACAA,MAAAA,QAAQ,GAAG,MAAM3Q,OAAO,CAACkU,gBAAgB,CAACpS,OAAO,CAAC,CAAA;SACrD,CACD,OAAOkV,UAAU,EAAE;UACf,IAAIA,UAAU,YAAYtc,KAAK,EAAE;IAC7BvB,QAAAA,KAAK,GAAG6d,UAAU,CAAA;IACtB,OAAA;IACJ,KAAA;IACA,IAAA,IAAIP,SAAS,EAAE;UACXQ,YAAY,CAACR,SAAS,CAAC,CAAA;IAC3B,KAAA;QAC2C;IACvC,MAAA,IAAI9F,QAAQ,EAAE;IACVsF,QAAAA,IAAI,CAAC3S,IAAI,CAAC,CAAA,0BAAA,CAA4B,CAAC,CAAA;IAC3C,OAAC,MACI;IACD2S,QAAAA,IAAI,CAAC3S,IAAI,CAAC,CAA0D,wDAAA,CAAA,GAChE,yBAAyB,CAAC,CAAA;IAClC,OAAA;IACJ,KAAA;IACA,IAAA,IAAInK,KAAK,IAAI,CAACwX,QAAQ,EAAE;IACpBA,MAAAA,QAAQ,GAAG,MAAM3Q,OAAO,CAACqU,UAAU,CAACvS,OAAO,CAAC,CAAA;UACD;IACvC,QAAA,IAAI6O,QAAQ,EAAE;cACVsF,IAAI,CAAC3S,IAAI,CAAC,CAAmC,gCAAA,EAAA,IAAI,CAACjF,SAAS,CAAA,CAAA,CAAG,GAAG,CAAA,OAAA,CAAS,CAAC,CAAA;IAC/E,SAAC,MACI;cACD4X,IAAI,CAAC3S,IAAI,CAAC,CAAA,0BAAA,EAA6B,IAAI,CAACjF,SAAS,UAAU,CAAC,CAAA;IACpE,SAAA;IACJ,OAAA;IACJ,KAAA;IACA,IAAA,OAAOsS,QAAQ,CAAA;IACnB,GAAA;IACJ;;ICnMA;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,SAASuG,YAAYA,GAAG;IACpB1e,EAAAA,IAAI,CAACoJ,gBAAgB,CAAC,UAAU,EAAE,MAAMpJ,IAAI,CAAC2e,OAAO,CAACC,KAAK,EAAE,CAAC,CAAA;IACjE;;IChBA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASzU,SAASA,CAACd,KAAK,EAAEwV,OAAO,EAAE;IAC/B,EAAA,MAAMC,aAAa,GAAGD,OAAO,EAAE,CAAA;IAC/BxV,EAAAA,KAAK,CAACc,SAAS,CAAC2U,aAAa,CAAC,CAAA;IAC9B,EAAA,OAAOA,aAAa,CAAA;IACxB;;ICnBA;IACA,IAAI;IACA9e,EAAAA,IAAI,CAAC,0BAA0B,CAAC,IAAIC,CAAC,EAAE,CAAA;IAC3C,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA,MAAM6e,qBAAqB,GAAG,iBAAiB,CAAA;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,SAASC,cAAcA,CAAC9b,KAAK,EAAE;MAClC,IAAI,CAACA,KAAK,EAAE;IACR,IAAA,MAAM,IAAIoD,YAAY,CAAC,mCAAmC,EAAE;IAAEpD,MAAAA,KAAAA;IAAM,KAAC,CAAC,CAAA;IAC1E,GAAA;IACA;IACA;IACA,EAAA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC3B,MAAM+b,SAAS,GAAG,IAAItW,GAAG,CAACzF,KAAK,EAAEmF,QAAQ,CAACD,IAAI,CAAC,CAAA;QAC/C,OAAO;UACHmR,QAAQ,EAAE0F,SAAS,CAAC7W,IAAI;UACxBhD,GAAG,EAAE6Z,SAAS,CAAC7W,IAAAA;SAClB,CAAA;IACL,GAAA;MACA,MAAM;QAAE8W,QAAQ;IAAE9Z,IAAAA,GAAAA;IAAI,GAAC,GAAGlC,KAAK,CAAA;MAC/B,IAAI,CAACkC,GAAG,EAAE;IACN,IAAA,MAAM,IAAIkB,YAAY,CAAC,mCAAmC,EAAE;IAAEpD,MAAAA,KAAAA;IAAM,KAAC,CAAC,CAAA;IAC1E,GAAA;IACA;IACA;MACA,IAAI,CAACgc,QAAQ,EAAE;QACX,MAAMD,SAAS,GAAG,IAAItW,GAAG,CAACvD,GAAG,EAAEiD,QAAQ,CAACD,IAAI,CAAC,CAAA;QAC7C,OAAO;UACHmR,QAAQ,EAAE0F,SAAS,CAAC7W,IAAI;UACxBhD,GAAG,EAAE6Z,SAAS,CAAC7W,IAAAA;SAClB,CAAA;IACL,GAAA;IACA;IACA;MACA,MAAM+W,WAAW,GAAG,IAAIxW,GAAG,CAACvD,GAAG,EAAEiD,QAAQ,CAACD,IAAI,CAAC,CAAA;MAC/C,MAAMgX,WAAW,GAAG,IAAIzW,GAAG,CAACvD,GAAG,EAAEiD,QAAQ,CAACD,IAAI,CAAC,CAAA;MAC/C+W,WAAW,CAACnG,YAAY,CAACpN,GAAG,CAACmT,qBAAqB,EAAEG,QAAQ,CAAC,CAAA;MAC7D,OAAO;QACH3F,QAAQ,EAAE4F,WAAW,CAAC/W,IAAI;QAC1BhD,GAAG,EAAEga,WAAW,CAAChX,IAAAA;OACpB,CAAA;IACL;;ICvDA;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMiX,2BAA2B,CAAC;IAC9B9Y,EAAAA,WAAWA,GAAG;QACV,IAAI,CAAC+Y,WAAW,GAAG,EAAE,CAAA;QACrB,IAAI,CAACC,cAAc,GAAG,EAAE,CAAA;QACxB,IAAI,CAACC,gBAAgB,GAAG,OAAO;UAAElW,OAAO;IAAEiT,MAAAA,KAAAA;IAAO,KAAC,KAAK;IACnD;IACA,MAAA,IAAIA,KAAK,EAAE;YACPA,KAAK,CAACtB,eAAe,GAAG3R,OAAO,CAAA;IACnC,OAAA;SACH,CAAA;QACD,IAAI,CAACoN,wBAAwB,GAAG,OAAO;UAAErN,KAAK;UAAEkT,KAAK;IAAE5F,MAAAA,cAAAA;IAAgB,KAAC,KAAK;IACzE,MAAA,IAAItN,KAAK,CAACpD,IAAI,KAAK,SAAS,EAAE;YAC1B,IAAIsW,KAAK,IACLA,KAAK,CAACtB,eAAe,IACrBsB,KAAK,CAACtB,eAAe,YAAY/Q,OAAO,EAAE;IAC1C;IACA,UAAA,MAAM9E,GAAG,GAAGmX,KAAK,CAACtB,eAAe,CAAC7V,GAAG,CAAA;IACrC,UAAA,IAAIuR,cAAc,EAAE;IAChB,YAAA,IAAI,CAAC4I,cAAc,CAACzU,IAAI,CAAC1F,GAAG,CAAC,CAAA;IACjC,WAAC,MACI;IACD,YAAA,IAAI,CAACka,WAAW,CAACxU,IAAI,CAAC1F,GAAG,CAAC,CAAA;IAC9B,WAAA;IACJ,SAAA;IACJ,OAAA;IACA,MAAA,OAAOuR,cAAc,CAAA;SACxB,CAAA;IACL,GAAA;IACJ;;IC1CA;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM8I,sBAAsB,CAAC;IACzBlZ,EAAAA,WAAWA,CAAC;IAAEmZ,IAAAA,kBAAAA;IAAmB,GAAC,EAAE;QAChC,IAAI,CAACC,kBAAkB,GAAG,OAAO;UAAErW,OAAO;IAAEoB,MAAAA,MAAAA;IAAQ,KAAC,KAAK;IACtD;IACA;IACA,MAAA,MAAM6O,QAAQ,GAAG,CAAC7O,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC6O,QAAQ,KAC7E,IAAI,CAACqG,mBAAmB,CAACC,iBAAiB,CAACvW,OAAO,CAAClE,GAAG,CAAC,CAAA;IAC3D;IACA,MAAA,OAAOmU,QAAQ,GACT,IAAIrP,OAAO,CAACqP,QAAQ,EAAE;YAAE/B,OAAO,EAAElO,OAAO,CAACkO,OAAAA;WAAS,CAAC,GACnDlO,OAAO,CAAA;SAChB,CAAA;QACD,IAAI,CAACsW,mBAAmB,GAAGF,kBAAkB,CAAA;IACjD,GAAA;IACJ;;IC5BA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMI,QAAQ,GAAGA,CAACC,UAAU,EAAEC,WAAW,KAAK;IAC1C7f,EAAAA,MAAM,CAACS,cAAc,CAACmf,UAAU,CAAC,CAAA;IACjC,EAAA,KAAK,MAAM3a,GAAG,IAAI4a,WAAW,EAAE;IAC3B7f,IAAAA,MAAM,CAACM,GAAG,CAAC2E,GAAG,CAAC,CAAA;IACnB,GAAA;MACAjF,MAAM,CAACU,QAAQ,EAAE,CAAA;IACrB,CAAC,CAAA;IACD;IACA;IACA;IACA;IACA;IACA;IACO,SAASof,mBAAmBA,CAACD,WAAW,EAAE;IAC7C,EAAA,MAAME,aAAa,GAAGF,WAAW,CAACvU,MAAM,CAAA;MACxC,IAAIyU,aAAa,GAAG,CAAC,EAAE;IACnB/f,IAAAA,MAAM,CAACS,cAAc,CAAC,6BAA6B,GAC/C,CAAA,EAAGsf,aAAa,CAAU,QAAA,CAAA,GAC1B,CAAUA,OAAAA,EAAAA,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG,QAAQ,WAAW,CAAC,CAAA;IACjEJ,IAAAA,QAAQ,CAAC,wBAAwB,EAAEE,WAAW,CAAC,CAAA;QAC/C7f,MAAM,CAACU,QAAQ,EAAE,CAAA;IACrB,GAAA;IACJ;;ICrCA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA,SAASsf,YAAYA,CAACJ,UAAU,EAAEK,IAAI,EAAE;IACpC,EAAA,IAAIA,IAAI,CAAC3U,MAAM,KAAK,CAAC,EAAE;IACnB,IAAA,OAAA;IACJ,GAAA;IACAtL,EAAAA,MAAM,CAACS,cAAc,CAACmf,UAAU,CAAC,CAAA;IACjC,EAAA,KAAK,MAAM3a,GAAG,IAAIgb,IAAI,EAAE;IACpBjgB,IAAAA,MAAM,CAACM,GAAG,CAAC2E,GAAG,CAAC,CAAA;IACnB,GAAA;MACAjF,MAAM,CAACU,QAAQ,EAAE,CAAA;IACrB,CAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,SAASwf,mBAAmBA,CAACC,cAAc,EAAEC,oBAAoB,EAAE;IACtE,EAAA,MAAMC,cAAc,GAAGF,cAAc,CAAC7U,MAAM,CAAA;IAC5C,EAAA,MAAMgV,qBAAqB,GAAGF,oBAAoB,CAAC9U,MAAM,CAAA;MACzD,IAAI+U,cAAc,IAAIC,qBAAqB,EAAE;IACzC,IAAA,IAAIlb,OAAO,GAAG,CAAcib,WAAAA,EAAAA,cAAc,CAAQA,KAAAA,EAAAA,cAAc,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,CAAG,CAAA,CAAA,CAAA;QACpF,IAAIC,qBAAqB,GAAG,CAAC,EAAE;IAC3Blb,MAAAA,OAAO,IACH,CAAA,CAAA,EAAIkb,qBAAqB,CAAA,CAAA,CAAG,GACxB,CAAA,IAAA,EAAOA,qBAAqB,KAAK,CAAC,GAAG,KAAK,GAAG,OAAO,CAAkB,gBAAA,CAAA,CAAA;IAClF,KAAA;IACAtgB,IAAAA,MAAM,CAACS,cAAc,CAAC2E,OAAO,CAAC,CAAA;IAC9B4a,IAAAA,YAAY,CAAC,CAAA,0BAAA,CAA4B,EAAEG,cAAc,CAAC,CAAA;IAC1DH,IAAAA,YAAY,CAAC,CAAA,+BAAA,CAAiC,EAAEI,oBAAoB,CAAC,CAAA;QACrEpgB,MAAM,CAACU,QAAQ,EAAE,CAAA;IACrB,GAAA;IACJ;;IC/CA;IACA;AACA;IACA;IACA;IACA;IACA;IAEA,IAAI6f,aAAa,CAAA;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASC,kCAAkCA,GAAG;MAC1C,IAAID,aAAa,KAAKhV,SAAS,EAAE;IAC7B,IAAA,MAAMkV,YAAY,GAAG,IAAIxI,QAAQ,CAAC,EAAE,CAAC,CAAA;QACrC,IAAI,MAAM,IAAIwI,YAAY,EAAE;UACxB,IAAI;IACA,QAAA,IAAIxI,QAAQ,CAACwI,YAAY,CAACC,IAAI,CAAC,CAAA;IAC/BH,QAAAA,aAAa,GAAG,IAAI,CAAA;WACvB,CACD,OAAO/f,KAAK,EAAE;IACV+f,QAAAA,aAAa,GAAG,KAAK,CAAA;IACzB,OAAA;IACJ,KAAA;IACAA,IAAAA,aAAa,GAAG,KAAK,CAAA;IACzB,GAAA;IACA,EAAA,OAAOA,aAAa,CAAA;IACxB;;ICjCA;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,eAAeI,YAAYA,CAAC3I,QAAQ,EAAE4I,QAAQ,EAAE;MAC5C,IAAIhb,MAAM,GAAG,IAAI,CAAA;IACjB;MACA,IAAIoS,QAAQ,CAAC/S,GAAG,EAAE;QACd,MAAM4b,WAAW,GAAG,IAAIrY,GAAG,CAACwP,QAAQ,CAAC/S,GAAG,CAAC,CAAA;QACzCW,MAAM,GAAGib,WAAW,CAACjb,MAAM,CAAA;IAC/B,GAAA;IACA,EAAA,IAAIA,MAAM,KAAK/F,IAAI,CAACqI,QAAQ,CAACtC,MAAM,EAAE;IACjC,IAAA,MAAM,IAAIO,YAAY,CAAC,4BAA4B,EAAE;IAAEP,MAAAA,MAAAA;IAAO,KAAC,CAAC,CAAA;IACpE,GAAA;IACA,EAAA,MAAMkb,cAAc,GAAG9I,QAAQ,CAACgD,KAAK,EAAE,CAAA;IACvC;IACA,EAAA,MAAM+F,YAAY,GAAG;IACjB1J,IAAAA,OAAO,EAAE,IAAI2J,OAAO,CAACF,cAAc,CAACzJ,OAAO,CAAC;QAC5C/R,MAAM,EAAEwb,cAAc,CAACxb,MAAM;QAC7B2b,UAAU,EAAEH,cAAc,CAACG,UAAAA;OAC9B,CAAA;IACD;MACA,MAAMC,oBAAoB,GAAGN,QAAQ,GAAGA,QAAQ,CAACG,YAAY,CAAC,GAAGA,YAAY,CAAA;IAC7E;IACA;IACA;IACA,EAAA,MAAML,IAAI,GAAGF,kCAAkC,EAAE,GAC3CM,cAAc,CAACJ,IAAI,GACnB,MAAMI,cAAc,CAACK,IAAI,EAAE,CAAA;IACjC,EAAA,OAAO,IAAIlJ,QAAQ,CAACyI,IAAI,EAAEQ,oBAAoB,CAAC,CAAA;IACnD;;ICvDA;IACA;AACA;IACA;IACA;IACA;IACA;IAQA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAME,gBAAgB,SAAS1E,QAAQ,CAAC;IACpC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACItW,EAAAA,WAAWA,CAAC2T,OAAO,GAAG,EAAE,EAAE;QACtBA,OAAO,CAACrU,SAAS,GAAGyH,UAAU,CAACI,eAAe,CAACwM,OAAO,CAACrU,SAAS,CAAC,CAAA;QACjE,KAAK,CAACqU,OAAO,CAAC,CAAA;QACd,IAAI,CAACsH,kBAAkB,GACnBtH,OAAO,CAACuH,iBAAiB,KAAK,KAAK,GAAG,KAAK,GAAG,IAAI,CAAA;IACtD;IACA;IACA;IACA;QACA,IAAI,CAAChH,OAAO,CAAC3P,IAAI,CAACyW,gBAAgB,CAACG,sCAAsC,CAAC,CAAA;IAC9E,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMvE,OAAOA,CAAC7T,OAAO,EAAE9B,OAAO,EAAE;QAC5B,MAAM2Q,QAAQ,GAAG,MAAM3Q,OAAO,CAACqU,UAAU,CAACvS,OAAO,CAAC,CAAA;IAClD,IAAA,IAAI6O,QAAQ,EAAE;IACV,MAAA,OAAOA,QAAQ,CAAA;IACnB,KAAA;IACA;IACA;QACA,IAAI3Q,OAAO,CAAC6B,KAAK,IAAI7B,OAAO,CAAC6B,KAAK,CAACpD,IAAI,KAAK,SAAS,EAAE;UACnD,OAAO,MAAM,IAAI,CAAC0b,cAAc,CAACrY,OAAO,EAAE9B,OAAO,CAAC,CAAA;IACtD,KAAA;IACA;IACA;QACA,OAAO,MAAM,IAAI,CAACoa,YAAY,CAACtY,OAAO,EAAE9B,OAAO,CAAC,CAAA;IACpD,GAAA;IACA,EAAA,MAAMoa,YAAYA,CAACtY,OAAO,EAAE9B,OAAO,EAAE;IACjC,IAAA,IAAI2Q,QAAQ,CAAA;IACZ,IAAA,MAAMzN,MAAM,GAAIlD,OAAO,CAACkD,MAAM,IAAI,EAAG,CAAA;IACrC;QACA,IAAI,IAAI,CAAC8W,kBAAkB,EAAE;UACkB;IACvCrhB,QAAAA,MAAM,CAACO,IAAI,CAAC,6BAA6B,GACrC,CAAA,EAAG+H,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,OAAO,IAAI,CAACS,SAAS,CAAW,SAAA,CAAA,GAC9D,qCAAqC,CAAC,CAAA;IAC9C,OAAA;IACA,MAAA,MAAMgc,mBAAmB,GAAGnX,MAAM,CAACoX,SAAS,CAAA;IAC5C,MAAA,MAAMC,kBAAkB,GAAGzY,OAAO,CAACwY,SAAS,CAAA;IAC5C,MAAA,MAAME,mBAAmB,GAAG,CAACD,kBAAkB,IAAIA,kBAAkB,KAAKF,mBAAmB,CAAA;IAC7F;IACA;UACA1J,QAAQ,GAAG,MAAM3Q,OAAO,CAACoT,KAAK,CAAC,IAAI1Q,OAAO,CAACZ,OAAO,EAAE;YAChDwY,SAAS,EAAExY,OAAO,CAACuR,IAAI,KAAK,SAAS,GAC/BkH,kBAAkB,IAAIF,mBAAmB,GACzCnW,SAAAA;IACV,OAAC,CAAC,CAAC,CAAA;IACH;IACA;IACA;IACA;IACA;IACA;IACA;UACA,IAAImW,mBAAmB,IACnBG,mBAAmB,IACnB1Y,OAAO,CAACuR,IAAI,KAAK,SAAS,EAAE;YAC5B,IAAI,CAACoH,qCAAqC,EAAE,CAAA;IAC5C,QAAA,MAAMC,SAAS,GAAG,MAAM1a,OAAO,CAACoU,QAAQ,CAACtS,OAAO,EAAE6O,QAAQ,CAACgD,KAAK,EAAE,CAAC,CAAA;YACxB;IACvC,UAAA,IAAI+G,SAAS,EAAE;IACX/hB,YAAAA,MAAM,CAACM,GAAG,CAAC,CAAA,eAAA,EAAkBgI,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,CAAG,CAAA,CAAA,GACvD,oCAAoC,CAAC,CAAA;IAC7C,WAAA;IACJ,SAAA;IACJ,OAAA;IACJ,KAAC,MACI;IACD;IACA;IACA,MAAA,MAAM,IAAIkB,YAAY,CAAC,wBAAwB,EAAE;YAC7CT,SAAS,EAAE,IAAI,CAACA,SAAS;YACzBT,GAAG,EAAEkE,OAAO,CAAClE,GAAAA;IACjB,OAAC,CAAC,CAAA;IACN,KAAA;QAC2C;IACvC,MAAA,MAAMmU,QAAQ,GAAG7O,MAAM,CAAC6O,QAAQ,KAAK,MAAM/R,OAAO,CAACuU,WAAW,CAACzS,OAAO,EAAE,MAAM,CAAC,CAAC,CAAA;IAChF;IACA;UACAnJ,MAAM,CAACS,cAAc,CAAC,CAA+B,6BAAA,CAAA,GAAG6H,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,CAAC,CAAA;IACpFjF,MAAAA,MAAM,CAACM,GAAG,CAAC,CAA8BgI,2BAAAA,EAAAA,cAAc,CAAC8Q,QAAQ,YAAYrP,OAAO,GAAGqP,QAAQ,CAACnU,GAAG,GAAGmU,QAAQ,CAAC,EAAE,CAAC,CAAA;IACjHpZ,MAAAA,MAAM,CAACS,cAAc,CAAC,CAAA,0BAAA,CAA4B,CAAC,CAAA;IACnDT,MAAAA,MAAM,CAACM,GAAG,CAAC6I,OAAO,CAAC,CAAA;UACnBnJ,MAAM,CAACU,QAAQ,EAAE,CAAA;IACjBV,MAAAA,MAAM,CAACS,cAAc,CAAC,CAAA,2BAAA,CAA6B,CAAC,CAAA;IACpDT,MAAAA,MAAM,CAACM,GAAG,CAAC0X,QAAQ,CAAC,CAAA;UACpBhY,MAAM,CAACU,QAAQ,EAAE,CAAA;UACjBV,MAAM,CAACU,QAAQ,EAAE,CAAA;IACrB,KAAA;IACA,IAAA,OAAOsX,QAAQ,CAAA;IACnB,GAAA;IACA,EAAA,MAAMwJ,cAAcA,CAACrY,OAAO,EAAE9B,OAAO,EAAE;QACnC,IAAI,CAACya,qCAAqC,EAAE,CAAA;QAC5C,MAAM9J,QAAQ,GAAG,MAAM3Q,OAAO,CAACoT,KAAK,CAACtR,OAAO,CAAC,CAAA;IAC7C;IACA;IACA,IAAA,MAAM4Y,SAAS,GAAG,MAAM1a,OAAO,CAACoU,QAAQ,CAACtS,OAAO,EAAE6O,QAAQ,CAACgD,KAAK,EAAE,CAAC,CAAA;QACnE,IAAI,CAAC+G,SAAS,EAAE;IACZ;IACA;IACA,MAAA,MAAM,IAAI5b,YAAY,CAAC,yBAAyB,EAAE;YAC9ClB,GAAG,EAAEkE,OAAO,CAAClE,GAAG;YAChBK,MAAM,EAAE0S,QAAQ,CAAC1S,MAAAA;IACrB,OAAC,CAAC,CAAA;IACN,KAAA;IACA,IAAA,OAAO0S,QAAQ,CAAA;IACnB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI8J,EAAAA,qCAAqCA,GAAG;QACpC,IAAIE,kBAAkB,GAAG,IAAI,CAAA;QAC7B,IAAIC,0BAA0B,GAAG,CAAC,CAAA;IAClC,IAAA,KAAK,MAAM,CAAC9Z,KAAK,EAAEqS,MAAM,CAAC,IAAI,IAAI,CAACF,OAAO,CAAC4H,OAAO,EAAE,EAAE;IAClD;IACA,MAAA,IAAI1H,MAAM,KAAK4G,gBAAgB,CAACG,sCAAsC,EAAE;IACpE,QAAA,SAAA;IACJ,OAAA;IACA;IACA,MAAA,IAAI/G,MAAM,KAAK4G,gBAAgB,CAACe,iCAAiC,EAAE;IAC/DH,QAAAA,kBAAkB,GAAG7Z,KAAK,CAAA;IAC9B,OAAA;UACA,IAAIqS,MAAM,CAAClC,eAAe,EAAE;IACxB2J,QAAAA,0BAA0B,EAAE,CAAA;IAChC,OAAA;IACJ,KAAA;QACA,IAAIA,0BAA0B,KAAK,CAAC,EAAE;UAClC,IAAI,CAAC3H,OAAO,CAAC3P,IAAI,CAACyW,gBAAgB,CAACe,iCAAiC,CAAC,CAAA;SACxE,MACI,IAAIF,0BAA0B,GAAG,CAAC,IAAID,kBAAkB,KAAK,IAAI,EAAE;IACpE;UACA,IAAI,CAAC1H,OAAO,CAACxO,MAAM,CAACkW,kBAAkB,EAAE,CAAC,CAAC,CAAA;IAC9C,KAAA;IACA;IACJ,GAAA;IACJ,CAAA;IACAZ,gBAAgB,CAACe,iCAAiC,GAAG;IACjD,EAAA,MAAM7J,eAAeA,CAAC;IAAEN,IAAAA,QAAAA;IAAS,GAAC,EAAE;QAChC,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAAC1S,MAAM,IAAI,GAAG,EAAE;IACrC,MAAA,OAAO,IAAI,CAAA;IACf,KAAA;IACA,IAAA,OAAO0S,QAAQ,CAAA;IACnB,GAAA;IACJ,CAAC,CAAA;IACDoJ,gBAAgB,CAACG,sCAAsC,GAAG;IACtD,EAAA,MAAMjJ,eAAeA,CAAC;IAAEN,IAAAA,QAAAA;IAAS,GAAC,EAAE;QAChC,OAAOA,QAAQ,CAACoK,UAAU,GAAG,MAAMzB,YAAY,CAAC3I,QAAQ,CAAC,GAAGA,QAAQ,CAAA;IACxE,GAAA;IACJ,CAAC;;IC7ND;IACA;AACA;IACA;IACA;IACA;IACA;IAaA;IACA;IACA;IACA;IACA;IACA,MAAMqK,kBAAkB,CAAC;IACrB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIjc,EAAAA,WAAWA,CAAC;QAAEV,SAAS;IAAE4U,IAAAA,OAAO,GAAG,EAAE;IAAEgH,IAAAA,iBAAiB,GAAG,IAAA;OAAO,GAAG,EAAE,EAAE;IACrE,IAAA,IAAI,CAACgB,gBAAgB,GAAG,IAAIzZ,GAAG,EAAE,CAAA;IACjC,IAAA,IAAI,CAAC0Z,iBAAiB,GAAG,IAAI1Z,GAAG,EAAE,CAAA;IAClC,IAAA,IAAI,CAAC2Z,uBAAuB,GAAG,IAAI3Z,GAAG,EAAE,CAAA;IACxC,IAAA,IAAI,CAACqR,SAAS,GAAG,IAAIkH,gBAAgB,CAAC;IAClC1b,MAAAA,SAAS,EAAEyH,UAAU,CAACI,eAAe,CAAC7H,SAAS,CAAC;IAChD4U,MAAAA,OAAO,EAAE,CACL,GAAGA,OAAO,EACV,IAAIgF,sBAAsB,CAAC;IAAEC,QAAAA,kBAAkB,EAAE,IAAA;IAAK,OAAC,CAAC,CAC3D;IACD+B,MAAAA,iBAAAA;IACJ,KAAC,CAAC,CAAA;IACF;QACA,IAAI,CAACmB,OAAO,GAAG,IAAI,CAACA,OAAO,CAACxN,IAAI,CAAC,IAAI,CAAC,CAAA;QACtC,IAAI,CAACyN,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACzN,IAAI,CAAC,IAAI,CAAC,CAAA;IAC5C,GAAA;IACA;IACJ;IACA;IACA;MACI,IAAI6E,QAAQA,GAAG;QACX,OAAO,IAAI,CAACI,SAAS,CAAA;IACzB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIzN,QAAQA,CAACyV,OAAO,EAAE;IACd,IAAA,IAAI,CAACS,cAAc,CAACT,OAAO,CAAC,CAAA;IAC5B,IAAA,IAAI,CAAC,IAAI,CAACU,+BAA+B,EAAE;UACvC/iB,IAAI,CAACoJ,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACwZ,OAAO,CAAC,CAAA;UAC9C5iB,IAAI,CAACoJ,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACyZ,QAAQ,CAAC,CAAA;UAChD,IAAI,CAACE,+BAA+B,GAAG,IAAI,CAAA;IAC/C,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;MACID,cAAcA,CAACT,OAAO,EAAE;QACuB;IACvC5a,MAAAA,kBAAM,CAAChB,OAAO,CAAC4b,OAAO,EAAE;IACpB/f,QAAAA,UAAU,EAAE,oBAAoB;IAChCC,QAAAA,SAAS,EAAE,oBAAoB;IAC/BC,QAAAA,QAAQ,EAAE,gBAAgB;IAC1BT,QAAAA,SAAS,EAAE,SAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,MAAMihB,eAAe,GAAG,EAAE,CAAA;IAC1B,IAAA,KAAK,MAAM9f,KAAK,IAAImf,OAAO,EAAE;IACzB;IACA,MAAA,IAAI,OAAOnf,KAAK,KAAK,QAAQ,EAAE;IAC3B8f,QAAAA,eAAe,CAAClY,IAAI,CAAC5H,KAAK,CAAC,CAAA;WAC9B,MACI,IAAIA,KAAK,IAAIA,KAAK,CAACgc,QAAQ,KAAKxT,SAAS,EAAE;IAC5CsX,QAAAA,eAAe,CAAClY,IAAI,CAAC5H,KAAK,CAACkC,GAAG,CAAC,CAAA;IACnC,OAAA;UACA,MAAM;YAAEmU,QAAQ;IAAEnU,QAAAA,GAAAA;IAAI,OAAC,GAAG4Z,cAAc,CAAC9b,KAAK,CAAC,CAAA;IAC/C,MAAA,MAAM+f,SAAS,GAAG,OAAO/f,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACgc,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAA;IACpF,MAAA,IAAI,IAAI,CAACuD,gBAAgB,CAAC1X,GAAG,CAAC3F,GAAG,CAAC,IAC9B,IAAI,CAACqd,gBAAgB,CAACzX,GAAG,CAAC5F,GAAG,CAAC,KAAKmU,QAAQ,EAAE;IAC7C,QAAA,MAAM,IAAIjT,YAAY,CAAC,uCAAuC,EAAE;cAC5DlD,UAAU,EAAE,IAAI,CAACqf,gBAAgB,CAACzX,GAAG,CAAC5F,GAAG,CAAC;IAC1C/B,UAAAA,WAAW,EAAEkW,QAAAA;IACjB,SAAC,CAAC,CAAA;IACN,OAAA;UACA,IAAI,OAAOrW,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAC4e,SAAS,EAAE;YAC9C,IAAI,IAAI,CAACa,uBAAuB,CAAC5X,GAAG,CAACwO,QAAQ,CAAC,IAC1C,IAAI,CAACoJ,uBAAuB,CAAC3X,GAAG,CAACuO,QAAQ,CAAC,KAAKrW,KAAK,CAAC4e,SAAS,EAAE;IAChE,UAAA,MAAM,IAAIxb,YAAY,CAAC,2CAA2C,EAAE;IAChElB,YAAAA,GAAAA;IACJ,WAAC,CAAC,CAAA;IACN,SAAA;YACA,IAAI,CAACud,uBAAuB,CAAC/W,GAAG,CAAC2N,QAAQ,EAAErW,KAAK,CAAC4e,SAAS,CAAC,CAAA;IAC/D,OAAA;UACA,IAAI,CAACW,gBAAgB,CAAC7W,GAAG,CAACxG,GAAG,EAAEmU,QAAQ,CAAC,CAAA;UACxC,IAAI,CAACmJ,iBAAiB,CAAC9W,GAAG,CAACxG,GAAG,EAAE6d,SAAS,CAAC,CAAA;IAC1C,MAAA,IAAID,eAAe,CAACvX,MAAM,GAAG,CAAC,EAAE;IAC5B,QAAA,MAAMyX,cAAc,GAAG,CAA8C,4CAAA,CAAA,GACjE,CAASF,MAAAA,EAAAA,eAAe,CAACzhB,IAAI,CAAC,IAAI,CAAC,CAAA,8BAAA,CAAgC,GACnE,CAA0C,wCAAA,CAAA,CAAA;YAMzC;IACDpB,UAAAA,MAAM,CAACO,IAAI,CAACwiB,cAAc,CAAC,CAAA;IAC/B,SAAA;IACJ,OAAA;IACJ,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIN,OAAOA,CAACvZ,KAAK,EAAE;IACX;IACA;IACA,IAAA,OAAOc,SAAS,CAACd,KAAK,EAAE,YAAY;IAChC,MAAA,MAAM8Z,mBAAmB,GAAG,IAAI9D,2BAA2B,EAAE,CAAA;UAC7D,IAAI,CAACpF,QAAQ,CAACQ,OAAO,CAAC3P,IAAI,CAACqY,mBAAmB,CAAC,CAAA;IAC/C;IACA;UACA,KAAK,MAAM,CAAC/d,GAAG,EAAEmU,QAAQ,CAAC,IAAI,IAAI,CAACkJ,gBAAgB,EAAE;YACjD,MAAMX,SAAS,GAAG,IAAI,CAACa,uBAAuB,CAAC3X,GAAG,CAACuO,QAAQ,CAAC,CAAA;YAC5D,MAAM0J,SAAS,GAAG,IAAI,CAACP,iBAAiB,CAAC1X,GAAG,CAAC5F,GAAG,CAAC,CAAA;IACjD,QAAA,MAAMkE,OAAO,GAAG,IAAIY,OAAO,CAAC9E,GAAG,EAAE;cAC7B0c,SAAS;IACT3L,UAAAA,KAAK,EAAE8M,SAAS;IAChBG,UAAAA,WAAW,EAAE,aAAA;IACjB,SAAC,CAAC,CAAA;YACF,MAAMrZ,OAAO,CAACC,GAAG,CAAC,IAAI,CAACiQ,QAAQ,CAAC8C,SAAS,CAAC;IACtCrS,UAAAA,MAAM,EAAE;IAAE6O,YAAAA,QAAAA;eAAU;cACpBjQ,OAAO;IACPD,UAAAA,KAAAA;IACJ,SAAC,CAAC,CAAC,CAAA;IACP,OAAA;UACA,MAAM;YAAEiW,WAAW;IAAEC,QAAAA,cAAAA;IAAe,OAAC,GAAG4D,mBAAmB,CAAA;UAChB;IACvC9C,QAAAA,mBAAmB,CAACf,WAAW,EAAEC,cAAc,CAAC,CAAA;IACpD,OAAA;UACA,OAAO;YAAED,WAAW;IAAEC,QAAAA,cAAAA;WAAgB,CAAA;IAC1C,KAAC,CAAC,CAAA;IACN,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIsD,QAAQA,CAACxZ,KAAK,EAAE;IACZ;IACA;IACA,IAAA,OAAOc,SAAS,CAACd,KAAK,EAAE,YAAY;IAChC,MAAA,MAAM8M,KAAK,GAAG,MAAMnW,IAAI,CAACoW,MAAM,CAACnE,IAAI,CAAC,IAAI,CAACgI,QAAQ,CAACpU,SAAS,CAAC,CAAA;IAC7D,MAAA,MAAMwd,uBAAuB,GAAG,MAAMlN,KAAK,CAACxU,IAAI,EAAE,CAAA;IAClD,MAAA,MAAM2hB,iBAAiB,GAAG,IAAIrV,GAAG,CAAC,IAAI,CAACwU,gBAAgB,CAACc,MAAM,EAAE,CAAC,CAAA;UACjE,MAAMvD,WAAW,GAAG,EAAE,CAAA;IACtB,MAAA,KAAK,MAAM1W,OAAO,IAAI+Z,uBAAuB,EAAE;YAC3C,IAAI,CAACC,iBAAiB,CAACvY,GAAG,CAACzB,OAAO,CAAClE,GAAG,CAAC,EAAE;IACrC,UAAA,MAAM+Q,KAAK,CAAChB,MAAM,CAAC7L,OAAO,CAAC,CAAA;IAC3B0W,UAAAA,WAAW,CAAClV,IAAI,CAACxB,OAAO,CAAClE,GAAG,CAAC,CAAA;IACjC,SAAA;IACJ,OAAA;UAC2C;YACvC6a,mBAAmB,CAACD,WAAW,CAAC,CAAA;IACpC,OAAA;UACA,OAAO;IAAEA,QAAAA,WAAAA;WAAa,CAAA;IAC1B,KAAC,CAAC,CAAA;IACN,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACIwD,EAAAA,kBAAkBA,GAAG;QACjB,OAAO,IAAI,CAACf,gBAAgB,CAAA;IAChC,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACIgB,EAAAA,aAAaA,GAAG;QACZ,OAAO,CAAC,GAAG,IAAI,CAAChB,gBAAgB,CAAC9gB,IAAI,EAAE,CAAC,CAAA;IAC5C,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIke,iBAAiBA,CAACza,GAAG,EAAE;QACnB,MAAM6Z,SAAS,GAAG,IAAItW,GAAG,CAACvD,GAAG,EAAEiD,QAAQ,CAACD,IAAI,CAAC,CAAA;QAC7C,OAAO,IAAI,CAACqa,gBAAgB,CAACzX,GAAG,CAACiU,SAAS,CAAC7W,IAAI,CAAC,CAAA;IACpD,GAAA;IACA;IACJ;IACA;IACA;IACA;MACIsb,uBAAuBA,CAACnK,QAAQ,EAAE;IAC9B,IAAA,OAAO,IAAI,CAACoJ,uBAAuB,CAAC3X,GAAG,CAACuO,QAAQ,CAAC,CAAA;IACrD,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI,MAAMoK,aAAaA,CAACra,OAAO,EAAE;QACzB,MAAMlE,GAAG,GAAGkE,OAAO,YAAYY,OAAO,GAAGZ,OAAO,CAAClE,GAAG,GAAGkE,OAAO,CAAA;IAC9D,IAAA,MAAMiQ,QAAQ,GAAG,IAAI,CAACsG,iBAAiB,CAACza,GAAG,CAAC,CAAA;IAC5C,IAAA,IAAImU,QAAQ,EAAE;IACV,MAAA,MAAMpD,KAAK,GAAG,MAAMnW,IAAI,CAACoW,MAAM,CAACnE,IAAI,CAAC,IAAI,CAACgI,QAAQ,CAACpU,SAAS,CAAC,CAAA;IAC7D,MAAA,OAAOsQ,KAAK,CAACvO,KAAK,CAAC2R,QAAQ,CAAC,CAAA;IAChC,KAAA;IACA,IAAA,OAAO7N,SAAS,CAAA;IACpB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;MACIkY,uBAAuBA,CAACxe,GAAG,EAAE;IACzB,IAAA,MAAMmU,QAAQ,GAAG,IAAI,CAACsG,iBAAiB,CAACza,GAAG,CAAC,CAAA;QAC5C,IAAI,CAACmU,QAAQ,EAAE;IACX,MAAA,MAAM,IAAIjT,YAAY,CAAC,mBAAmB,EAAE;IAAElB,QAAAA,GAAAA;IAAI,OAAC,CAAC,CAAA;IACxD,KAAA;IACA,IAAA,OAAQ8U,OAAO,IAAK;IAChBA,MAAAA,OAAO,CAAC5Q,OAAO,GAAG,IAAIY,OAAO,CAAC9E,GAAG,CAAC,CAAA;IAClC8U,MAAAA,OAAO,CAACxP,MAAM,GAAGhJ,MAAM,CAAC0X,MAAM,CAAC;IAAEG,QAAAA,QAAAA;IAAS,OAAC,EAAEW,OAAO,CAACxP,MAAM,CAAC,CAAA;IAC5D,MAAA,OAAO,IAAI,CAACuP,QAAQ,CAACvS,MAAM,CAACwS,OAAO,CAAC,CAAA;SACvC,CAAA;IACL,GAAA;IACJ;;IClSA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA,IAAIwF,kBAAkB,CAAA;IACtB;IACA;IACA;IACA;IACO,MAAMmE,6BAA6B,GAAGA,MAAM;MAC/C,IAAI,CAACnE,kBAAkB,EAAE;IACrBA,IAAAA,kBAAkB,GAAG,IAAI8C,kBAAkB,EAAE,CAAA;IACjD,GAAA;IACA,EAAA,OAAO9C,kBAAkB,CAAA;IAC7B,CAAC;;ICnBD;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,SAASoE,yBAAyBA,CAAC7E,SAAS,EAAE8E,2BAA2B,GAAG,EAAE,EAAE;IACnF;IACA;IACA,EAAA,KAAK,MAAMhiB,SAAS,IAAI,CAAC,GAAGkd,SAAS,CAACjG,YAAY,CAACrX,IAAI,EAAE,CAAC,EAAE;IACxD,IAAA,IAAIoiB,2BAA2B,CAACxV,IAAI,CAAEvG,MAAM,IAAKA,MAAM,CAAC/G,IAAI,CAACc,SAAS,CAAC,CAAC,EAAE;IACtEkd,MAAAA,SAAS,CAACjG,YAAY,CAAC7D,MAAM,CAACpT,SAAS,CAAC,CAAA;IAC5C,KAAA;IACJ,GAAA;IACA,EAAA,OAAOkd,SAAS,CAAA;IACpB;;IC7BA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,UAAU+E,qBAAqBA,CAAC5e,GAAG,EAAE;IAAE2e,EAAAA,2BAA2B,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC;IAAEE,EAAAA,cAAc,GAAG,YAAY;IAAEC,EAAAA,SAAS,GAAG,IAAI;IAAEC,EAAAA,eAAAA;IAAiB,CAAC,GAAG,EAAE,EAAE;MACzK,MAAMlF,SAAS,GAAG,IAAItW,GAAG,CAACvD,GAAG,EAAEiD,QAAQ,CAACD,IAAI,CAAC,CAAA;MAC7C6W,SAAS,CAACzL,IAAI,GAAG,EAAE,CAAA;MACnB,MAAMyL,SAAS,CAAC7W,IAAI,CAAA;IACpB,EAAA,MAAMgc,uBAAuB,GAAGN,yBAAyB,CAAC7E,SAAS,EAAE8E,2BAA2B,CAAC,CAAA;MACjG,MAAMK,uBAAuB,CAAChc,IAAI,CAAA;MAClC,IAAI6b,cAAc,IAAIG,uBAAuB,CAAC7X,QAAQ,CAAC8X,QAAQ,CAAC,GAAG,CAAC,EAAE;QAClE,MAAMC,YAAY,GAAG,IAAI3b,GAAG,CAACyb,uBAAuB,CAAChc,IAAI,CAAC,CAAA;QAC1Dkc,YAAY,CAAC/X,QAAQ,IAAI0X,cAAc,CAAA;QACvC,MAAMK,YAAY,CAAClc,IAAI,CAAA;IAC3B,GAAA;IACA,EAAA,IAAI8b,SAAS,EAAE;QACX,MAAMK,QAAQ,GAAG,IAAI5b,GAAG,CAACyb,uBAAuB,CAAChc,IAAI,CAAC,CAAA;QACtDmc,QAAQ,CAAChY,QAAQ,IAAI,OAAO,CAAA;QAC5B,MAAMgY,QAAQ,CAACnc,IAAI,CAAA;IACvB,GAAA;IACA,EAAA,IAAI+b,eAAe,EAAE;QACjB,MAAMK,cAAc,GAAGL,eAAe,CAAC;IAAE/e,MAAAA,GAAG,EAAE6Z,SAAAA;IAAU,KAAC,CAAC,CAAA;IAC1D,IAAA,KAAK,MAAMwF,YAAY,IAAID,cAAc,EAAE;UACvC,MAAMC,YAAY,CAACrc,IAAI,CAAA;IAC3B,KAAA;IACJ,GAAA;IACJ;;ICzCA;IACA;AACA;IACA;IACA;IACA;IACA;IAMA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMsc,aAAa,SAAS/c,KAAK,CAAC;IAC9B;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIpB,EAAAA,WAAWA,CAACmZ,kBAAkB,EAAExF,OAAO,EAAE;QACrC,MAAMtS,KAAK,GAAGA,CAAC;IAAE0B,MAAAA,OAAAA;IAAS,KAAC,KAAK;IAC5B,MAAA,MAAMqb,eAAe,GAAGjF,kBAAkB,CAAC8D,kBAAkB,EAAE,CAAA;UAC/D,KAAK,MAAMoB,WAAW,IAAIZ,qBAAqB,CAAC1a,OAAO,CAAClE,GAAG,EAAE8U,OAAO,CAAC,EAAE;IACnE,QAAA,MAAMX,QAAQ,GAAGoL,eAAe,CAAC3Z,GAAG,CAAC4Z,WAAW,CAAC,CAAA;IACjD,QAAA,IAAIrL,QAAQ,EAAE;IACV,UAAA,MAAMuI,SAAS,GAAGpC,kBAAkB,CAACgE,uBAAuB,CAACnK,QAAQ,CAAC,CAAA;cACtE,OAAO;gBAAEA,QAAQ;IAAEuI,YAAAA,SAAAA;eAAW,CAAA;IAClC,SAAA;IACJ,OAAA;UAC2C;YACvC3hB,MAAM,CAACK,KAAK,CAAC,CAAsC,oCAAA,CAAA,GAAGiI,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,CAAC,CAAA;IACtF,OAAA;IACA,MAAA,OAAA;SACH,CAAA;IACD,IAAA,KAAK,CAACwC,KAAK,EAAE8X,kBAAkB,CAACzF,QAAQ,CAAC,CAAA;IAC7C,GAAA;IACJ;;ICvDA;IACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAAS4K,QAAQA,CAAC3K,OAAO,EAAE;IACvB,EAAA,MAAMwF,kBAAkB,GAAGmE,6BAA6B,EAAE,CAAA;MAC1D,MAAMiB,aAAa,GAAG,IAAIJ,aAAa,CAAChF,kBAAkB,EAAExF,OAAO,CAAC,CAAA;MACpErO,aAAa,CAACiZ,aAAa,CAAC,CAAA;IAChC;;IC7BA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASlY,QAAQA,CAACyV,OAAO,EAAE;IACvB,EAAA,MAAM3C,kBAAkB,GAAGmE,6BAA6B,EAAE,CAAA;IAC1DnE,EAAAA,kBAAkB,CAAC9S,QAAQ,CAACyV,OAAO,CAAC,CAAA;IACxC;;IC/BA;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAAS0C,gBAAgBA,CAAC1C,OAAO,EAAEnI,OAAO,EAAE;MACxCtN,QAAQ,CAACyV,OAAO,CAAC,CAAA;MACjBwC,QAAQ,CAAC3K,OAAO,CAAC,CAAA;IACrB;;IC3BA;IACA;AACA;IACA;IACA;IACA;IACA;IAEA,MAAM8K,iBAAiB,GAAG,YAAY,CAAA;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,oBAAoB,GAAG,OAAOC,mBAAmB,EAAEC,eAAe,GAAGH,iBAAiB,KAAK;MAC7F,MAAM1X,UAAU,GAAG,MAAMtN,IAAI,CAACoW,MAAM,CAACzU,IAAI,EAAE,CAAA;IAC3C,EAAA,MAAMyjB,kBAAkB,GAAG9X,UAAU,CAACH,MAAM,CAAEtH,SAAS,IAAK;QACxD,OAAQA,SAAS,CAACoB,QAAQ,CAACke,eAAe,CAAC,IACvCtf,SAAS,CAACoB,QAAQ,CAACjH,IAAI,CAACgN,YAAY,CAACC,KAAK,CAAC,IAC3CpH,SAAS,KAAKqf,mBAAmB,CAAA;IACzC,GAAC,CAAC,CAAA;IACF,EAAA,MAAMnb,OAAO,CAACC,GAAG,CAACob,kBAAkB,CAACnb,GAAG,CAAEpE,SAAS,IAAK7F,IAAI,CAACoW,MAAM,CAACjB,MAAM,CAACtP,SAAS,CAAC,CAAC,CAAC,CAAA;IACvF,EAAA,OAAOuf,kBAAkB,CAAA;IAC7B,CAAC;;ICpCD;IACA;AACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA,SAASC,qBAAqBA,GAAG;IAC7B;IACArlB,EAAAA,IAAI,CAACoJ,gBAAgB,CAAC,UAAU,EAAIC,KAAK,IAAK;IAC1C,IAAA,MAAMxD,SAAS,GAAGyH,UAAU,CAACI,eAAe,EAAE,CAAA;QAC9CrE,KAAK,CAACc,SAAS,CAAC8a,oBAAoB,CAACpf,SAAS,CAAC,CAACwE,IAAI,CAAEib,aAAa,IAAK;UACzB;IACvC,QAAA,IAAIA,aAAa,CAAC7Z,MAAM,GAAG,CAAC,EAAE;cAC1BtL,MAAM,CAACM,GAAG,CAAC,CAAA,oDAAA,CAAsD,GAC7D,CAAgB,cAAA,CAAA,EAAE6kB,aAAa,CAAC,CAAA;IACxC,SAAA;IACJ,OAAA;IACJ,KAAC,CAAC,CAAC,CAAA;IACP,GAAE,CAAC,CAAA;IACP;;IC9BA;IACA;AACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,eAAe,SAAS5d,KAAK,CAAC;IAChC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIpB,WAAWA,CAACiB,OAAO,EAAE;QAAEge,SAAS,GAAG,CAAC,GAAG,CAAC;IAAEC,IAAAA,QAAQ,GAAG,EAAA;OAAI,GAAG,EAAE,EAAE;QACjB;IACvChe,MAAAA,kBAAM,CAACP,cAAc,CAACse,SAAS,EAAEvd,MAAM,EAAE;IACrC3F,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,iBAAiB;IAC5BC,QAAAA,QAAQ,EAAE,aAAa;IACvBT,QAAAA,SAAS,EAAE,mBAAA;IACf,OAAC,CAAC,CAAA;IACF0F,MAAAA,kBAAM,CAACP,cAAc,CAACue,QAAQ,EAAExd,MAAM,EAAE;IACpC3F,QAAAA,UAAU,EAAE,iBAAiB;IAC7BC,QAAAA,SAAS,EAAE,iBAAiB;IAC5BC,QAAAA,QAAQ,EAAE,aAAa;IACvBT,QAAAA,SAAS,EAAE,kBAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,KAAK,CAAEmY,OAAO,IAAK,IAAI,CAACwL,MAAM,CAACxL,OAAO,CAAC,EAAE1S,OAAO,CAAC,CAAA;QACjD,IAAI,CAACme,UAAU,GAAGH,SAAS,CAAA;QAC3B,IAAI,CAACI,SAAS,GAAGH,QAAQ,CAAA;IAC7B,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIC,EAAAA,MAAMA,CAAC;QAAEtgB,GAAG;IAAEkE,IAAAA,OAAAA;IAAQ,GAAC,EAAE;IACrB,IAAA,IAAIA,OAAO,IAAIA,OAAO,CAACuR,IAAI,KAAK,UAAU,EAAE;IACxC,MAAA,OAAO,KAAK,CAAA;IAChB,KAAA;QACA,MAAMgL,iBAAiB,GAAGzgB,GAAG,CAACmH,QAAQ,GAAGnH,GAAG,CAAC0gB,MAAM,CAAA;IACnD,IAAA,KAAK,MAAM9d,MAAM,IAAI,IAAI,CAAC4d,SAAS,EAAE;IACjC,MAAA,IAAI5d,MAAM,CAAC/G,IAAI,CAAC4kB,iBAAiB,CAAC,EAAE;YACW;IACvC1lB,UAAAA,MAAM,CAACM,GAAG,CAAC,CAAwBolB,qBAAAA,EAAAA,iBAAiB,UAAU,GAC1D,CAAA,yDAAA,CAA2D,GAC3D,CAAA,EAAG7d,MAAM,CAACO,QAAQ,EAAE,EAAE,CAAC,CAAA;IAC/B,SAAA;IACA,QAAA,OAAO,KAAK,CAAA;IAChB,OAAA;IACJ,KAAA;IACA,IAAA,IAAI,IAAI,CAACod,UAAU,CAACpX,IAAI,CAAEvG,MAAM,IAAKA,MAAM,CAAC/G,IAAI,CAAC4kB,iBAAiB,CAAC,CAAC,EAAE;UACvB;YACvC1lB,MAAM,CAACK,KAAK,CAAC,CAAA,qBAAA,EAAwBqlB,iBAAiB,CAAG,CAAA,CAAA,GAAG,gBAAgB,CAAC,CAAA;IACjF,OAAA;IACA,MAAA,OAAO,IAAI,CAAA;IACf,KAAA;QAC2C;UACvC1lB,MAAM,CAACM,GAAG,CAAC,CAAwBolB,qBAAAA,EAAAA,iBAAiB,UAAU,GAC1D,CAAA,qDAAA,CAAuD,GACvD,CAAA,oBAAA,CAAsB,CAAC,CAAA;IAC/B,KAAA;IACA,IAAA,OAAO,KAAK,CAAA;IAChB,GAAA;IACJ;;IC5GA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASjC,uBAAuBA,CAACxe,GAAG,EAAE;IAClC,EAAA,MAAMsa,kBAAkB,GAAGmE,6BAA6B,EAAE,CAAA;IAC1D,EAAA,OAAOnE,kBAAkB,CAACkE,uBAAuB,CAACxe,GAAG,CAAC,CAAA;IAC1D;;;;;;;;;;;;;;;;;"}